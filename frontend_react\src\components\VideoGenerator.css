.video-generator {
  max-width: 800px;
  margin: 0 auto;
}

.video-generator h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 28px;
}

.subtitle {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.5;
}

.generator-form {
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.topic-input, .format-select {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
}

.topic-input:focus, .format-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.topic-input:disabled, .format-select:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
}

.example-topics {
  margin-top: 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.example-topics span {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.example-tag {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
}

.example-tag:hover:not(:disabled) {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.example-tag:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.generate-button {
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 18px 24px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.generate-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.generate-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-message {
  background: #fee;
  color: #c33;
  padding: 15px;
  border-radius: 10px;
  margin-top: 20px;
  border: 1px solid #fcc;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
}

.current-job {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 25px;
  margin-top: 30px;
  border: 1px solid #e9ecef;
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.job-header h3 {
  color: #2c3e50;
  margin: 0;
  font-size: 20px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.processing {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.failed {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.job-details {
  margin-bottom: 20px;
}

.job-details p {
  margin: 8px 0;
  color: #555;
  font-size: 14px;
}

.results-section {
  margin-top: 20px;
}

.video-preview, .audio-preview, .script-preview {
  margin-bottom: 25px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.video-preview h4, .audio-preview h4, .script-preview h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-container, .audio-container {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.preview-video {
  width: 100%;
  height: auto;
  max-height: 400px;
  display: block;
}

.preview-audio {
  width: 100%;
  height: 54px;
}

.script-container {
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.preview-script {
  width: 100%;
  height: 200px;
  border: none;
  background: white;
  padding: 15px;
  font-family: 'Georgia', serif;
  line-height: 1.6;
  color: #333;
}

.download-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.download-section h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 16px;
}

.download-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
}

.download-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.download-button:hover {
  background: #218838;
  transform: translateY(-1px);
}

/* Responsive */
@media (max-width: 768px) {
  .video-generator {
    max-width: 100%;
  }
  
  .example-topics {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .job-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .download-grid {
    grid-template-columns: 1fr;
  }

  .video-preview, .audio-preview, .script-preview {
    padding: 15px;
  }

  .preview-video {
    max-height: 250px;
  }

  .preview-script {
    height: 150px;
    padding: 12px;
    font-size: 14px;
  }
}

/* Complete Video Styles */
.complete-video-preview {
  border: 2px solid #4CAF50;
  border-radius: 12px;
  background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
  margin-bottom: 25px;
  position: relative;
}

.complete-video-preview::before {
  content: "✨ RECOMMENDED";
  position: absolute;
  top: -10px;
  right: 15px;
  background: #4CAF50;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  z-index: 1;
}

.complete-video-preview h4 {
  color: #2E7D32;
  margin-bottom: 15px;
}

.preview-video.complete-video {
  border: 2px solid #4CAF50;
  border-radius: 8px;
}

.video-description {
  margin-top: 10px;
  color: #2E7D32;
  font-size: 14px;
  font-style: italic;
  text-align: center;
}

/* Download Button Styles */
.download-button.primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border: none;
  position: relative;
  font-weight: 600;
}

.download-button.primary:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.primary-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #FF9800;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: bold;
}

/* Script Mode Styles */
.script-mode-label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  margin-bottom: 12px;
}

.script-mode-options {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #555;
}

.radio-option input[type="radio"] {
  margin: 0;
  cursor: pointer;
}

.radio-option:hover {
  color: #333;
}

.custom-script-input {
  width: 100%;
  padding: 15px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 14px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.5;
  resize: vertical;
  min-height: 120px;
  transition: all 0.3s ease;
  background: white;
}

.custom-script-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.custom-script-input::placeholder {
  color: #999;
  font-style: italic;
}

.script-tips {
  margin-top: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.script-tips p {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #555;
  font-weight: 600;
}

.script-tips ul {
  margin: 0;
  padding-left: 18px;
  font-size: 12px;
  color: #666;
}

.script-tips li {
  margin-bottom: 4px;
  line-height: 1.4;
}
