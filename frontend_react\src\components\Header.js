import React from 'react';
import { Play, Sparkles } from 'lucide-react';
import './Header.css';

const Header = () => {
  return (
    <header className="header">
      <div className="header-content">
        <div className="logo">
          <div className="logo-icon">
            <Play size={32} />
            <Sparkles size={20} className="sparkle" />
          </div>
          <div className="logo-text">
            <h1>3Blue1Brown</h1>
            <span>Video Generator</span>
          </div>
        </div>
        
        <div className="header-info">
          <div className="status-indicator">
            <div className="status-dot"></div>
            <span>API Ready</span>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
