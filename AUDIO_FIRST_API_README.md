# Audio-First 3Blue1Brown Video Generator API

🎵 **REST API for generating educational videos with natural speech pacing and synchronized animations**

## 🌟 Features

- **Audio-First Generation** - Natural speech pacing drives video timing
- **Enhanced Validation** - Comprehensive error detection and correction
- **Synchronized Animations** - Perfect audio-visual synchronization
- **3Blue1Brown Style** - Authentic mathematical visualizations
- **Backup API Keys** - Automatic failover for quota management
- **RESTful API** - Clean, documented endpoints
- **Asynchronous Processing** - Background job processing with status tracking
- **Multiple Output Formats** - MP4/GIF support
- **File Management** - Download, list, and delete generated content

## 🚀 Quick Start

### 1. Start the Audio-First API Server

```bash
# Activate virtual environment
.venv\Scripts\activate

# Start the audio-first API server
python api_audio_first.py
```

The server will start on `http://localhost:8001`

### 2. Access API Documentation

- **Swagger UI**: http://localhost:8001/docs
- **ReDoc**: http://localhost:8001/redoc
- **Health Check**: http://localhost:8001/health

## 📋 API Endpoints

### Core Generation

#### 1. Generate Audio-First Video

**POST** `/generate-audio-first`

```json
{
  "topic": "quadratic functions and parabolas",
  "output_format": "mp4",
  "natural_pacing": true,
  "include_audio": true,
  "job_id": "optional_custom_id"
}
```

**Response:**
```json
{
  "job_id": "audio_a1b2c3d4",
  "status": "accepted",
  "message": "Audio-first video generation started",
  "topic": "quadratic functions and parabolas",
  "output_format": "mp4",
  "natural_pacing": true,
  "include_audio": true,
  "created_at": "2024-01-15T10:30:00"
}
```

#### 2. Check Job Status

**GET** `/status/{job_id}`

**Response:**
```json
{
  "job_id": "audio_a1b2c3d4",
  "status": "completed",
  "progress": "Audio-first video generation completed successfully",
  "topic": "quadratic functions and parabolas",
  "output_format": "mp4",
  "natural_pacing": true,
  "include_audio": true,
  "created_at": "2024-01-15T10:30:00",
  "completed_at": "2024-01-15T10:33:45",
  "audio_duration": 155.14,
  "audio_segments": 19,
  "files": {
    "complete_video": "generated/audio_a1b2c3d4/complete_video.mp4",
    "video": "generated/audio_a1b2c3d4/UnitCircleTrig.mp4",
    "audio": "generated/audio_a1b2c3d4/voiceover.mp3",
    "script": "generated/audio_a1b2c3d4/script.txt",
    "code": "generated/audio_a1b2c3d4/manim_code.py"
  }
}
```

#### 3. Download Files

**GET** `/download/{job_id}/{file_type}`

Available file types: `complete_video`, `video`, `audio`, `script`, `code`

### Management Endpoints

#### 4. List All Jobs

**GET** `/jobs`

#### 5. Get Job Files Info

**GET** `/jobs/{job_id}/files`

#### 6. Delete Job

**DELETE** `/jobs/{job_id}`

#### 7. Health Check

**GET** `/health`

## 🐍 Python Client Usage

```python
from api_audio_first_client import AudioFirstVideoGeneratorClient

# Initialize client
client = AudioFirstVideoGeneratorClient("http://localhost:8001")

# Check health
health = client.health_check()
print(f"API Status: {health['status']}")

# Generate audio-first video
result = client.generate_audio_first_video(
    topic="trigonometry and unit circle",
    output_format="mp4",
    natural_pacing=True,
    include_audio=True
)

job_id = result['job_id']

# Wait for completion
final_status = client.wait_for_completion(job_id)

# Download complete video
client.download_file(job_id, "complete_video", f"{job_id}_complete.mp4")
```

## 🌐 cURL Examples

### Generate Video
```bash
curl -X POST "http://localhost:8001/generate-audio-first" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "derivatives and rates of change",
    "output_format": "mp4",
    "natural_pacing": true,
    "include_audio": true
  }'
```

### Check Status
```bash
curl "http://localhost:8001/status/audio_a1b2c3d4"
```

### Download Video
```bash
curl -O "http://localhost:8001/download/audio_a1b2c3d4/complete_video"
```

## 🎵 Audio-First Features

### Natural Speech Pacing
- **Conversational Flow**: Natural speaking rhythm and pauses
- **Segment-Based**: Audio broken into logical segments
- **Timing Synchronization**: Video animations match audio timing exactly

### Enhanced Validation
- **Syntax Error Prevention**: Comprehensive code validation
- **3Blue1Brown Compatibility**: Ensures authentic manim syntax
- **Error Correction**: LLM learns from validation feedback
- **Multiple Validation Layers**: Syntax, imports, animations, coordinates

### Mathematical Visualizations
- **Dynamic Animations**: Real-time mathematical objects
- **Coordinate Systems**: Proper axes and scaling
- **Function Graphs**: Animated plotting and transformations
- **Interactive Elements**: Rotating lines, moving points, transforming equations

## 🔧 Configuration

### Environment Variables
```bash
GOOGLE_API_KEY=your_primary_key
GOOGLE_API_KEY2=your_backup_key
```

### API Settings
- **Port**: 8001 (configurable)
- **Host**: 0.0.0.0 (all interfaces)
- **Reload**: True (development mode)
- **Log Level**: INFO

## 📊 Response Status Codes

- **200**: Success
- **400**: Bad Request (invalid parameters)
- **404**: Job/File Not Found
- **500**: Internal Server Error
- **503**: Service Unavailable (system not initialized)

## 🎯 Job Status Values

- **pending**: Job queued for processing
- **processing**: Generation in progress
- **completed**: Successfully completed
- **failed**: Generation failed

## 🧪 Testing

### Run Full API Test
```bash
python test_audio_first_api.py
```

### Quick Endpoint Test
```bash
python test_audio_first_api.py --quick
```

### Interactive Client Test
```bash
python api_audio_first_client.py
```

## 📁 Output Structure

```
generated/
└── audio_[job_id]/
    ├── complete_video.mp4      # Final video with integrated audio
    ├── [SceneName].mp4         # Manim video (no audio)
    ├── voiceover.mp3           # Natural speech audio
    ├── script.txt              # Educational script
    ├── manim_code.py           # 3Blue1Brown manim code
    └── audio_timing.json       # Timing synchronization data
```

## 🎉 Key Advantages

✅ **Natural Speech**: Conversational pacing instead of robotic TTS  
✅ **Perfect Sync**: Audio timing drives video generation  
✅ **Error-Free**: Enhanced validation prevents common mistakes  
✅ **Professional Quality**: 3Blue1Brown authentic animations  
✅ **Robust**: Backup API keys and comprehensive error handling  
✅ **Scalable**: RESTful API with async processing  

This audio-first API provides the most natural and professional educational video generation experience available! 🎵🎬
