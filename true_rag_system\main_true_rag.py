"""
Main script for True RAG Enhanced Manim Video Generation

This script demonstrates the True RAG system with:
1. Multi-dimensional context analysis
2. Multi-source knowledge integration
3. Intelligent knowledge synthesis
4. Adaptive learning capabilities
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_llm import TrueRAGEnhancedManimLLM
from app.true_rag_system import TrueRAGSystem

def setup_true_rag_system():
    """Setup and initialize the True RAG system"""
    
    print("🧠 Setting up True RAG System")
    print("=" * 50)
    
    # Initialize the True RAG system
    rag_system = TrueRAGSystem("true_rag_db")
    rag_system.initialize_knowledge_bases()
    
    # Add some sample knowledge (this would normally be done during setup)
    print("📚 Adding sample knowledge to the system...")
    
    # Sample manim examples
    sample_examples = [
        {
            'code': '''
from manimlib import *

class DerivativeVisualization(Scene):
    def construct(self):
        axes = Axes(x_range=(-3, 3, 1), y_range=(-2, 8, 1))
        graph = axes.get_graph(lambda x: x**2, color=BLUE)
        
        self.play(ShowCreation(axes))
        self.play(ShowCreation(graph))
        
        # Show tangent line
        tangent = axes.get_tangent_line(graph, 2, length=3, color=RED)
        self.play(ShowCreation(tangent))
        
        self.wait(2)
''',
            'metadata': {
                'title': 'Derivative Visualization',
                'concepts': ['derivative', 'tangent_line', 'calculus'],
                'complexity': 'intermediate',
                'duration': 10,
                'objects_used': ['Axes', 'ParametricFunction', 'Line']
            }
        },
        {
            'code': '''
from manimlib import *

class VectorAddition(Scene):
    def construct(self):
        # Create vectors
        vector1 = Arrow(ORIGIN, [2, 1, 0], color=RED)
        vector2 = Arrow(ORIGIN, [1, 2, 0], color=BLUE)
        
        self.play(ShowCreation(vector1))
        self.play(ShowCreation(vector2))
        
        # Show vector addition
        vector2_moved = vector2.copy()
        vector2_moved.shift([2, 1, 0])
        
        self.play(Transform(vector2, vector2_moved))
        
        # Result vector
        result = Arrow(ORIGIN, [3, 3, 0], color=GREEN)
        self.play(ShowCreation(result))
        
        self.wait(2)
''',
            'metadata': {
                'title': 'Vector Addition',
                'concepts': ['vector', 'addition', 'linear_algebra'],
                'complexity': 'basic',
                'duration': 12,
                'objects_used': ['Arrow', 'Transform']
            }
        }
    ]
    
    for example in sample_examples:
        rag_system.add_manim_example(example['code'], example['metadata'])
    
    # Sample mathematical concepts
    sample_concepts = [
        {
            'name': 'derivative',
            'data': {
                'category': 'calculus',
                'difficulty_level': 3,
                'prerequisites': ['function', 'limit', 'slope'],
                'visual_patterns': ['tangent_line', 'rate_of_change', 'slope_visualization'],
                'common_errors': ['confusing_derivative_with_integral', 'wrong_chain_rule']
            }
        },
        {
            'name': 'vector',
            'data': {
                'category': 'linear_algebra',
                'difficulty_level': 2,
                'prerequisites': ['coordinate_system', 'direction', 'magnitude'],
                'visual_patterns': ['arrow_representation', 'component_breakdown', 'vector_addition'],
                'common_errors': ['confusing_vector_with_scalar', 'wrong_addition_rule']
            }
        }
    ]
    
    for concept in sample_concepts:
        rag_system.add_mathematical_concept(concept['name'], concept['data'])
    
    print("✅ True RAG system setup complete!")
    return rag_system

def test_true_rag_generation():
    """Test the True RAG enhanced generation"""
    
    print("\n🎬 Testing True RAG Enhanced Generation")
    print("=" * 50)
    
    # Initialize the True RAG Enhanced LLM
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    
    # Test topics
    test_topics = [
        "derivatives and tangent lines",
        "vector addition in 2D",
        "area under a curve",
        "pythagorean theorem proof"
    ]
    
    for i, topic in enumerate(test_topics, 1):
        print(f"\n🎯 Test {i}: {topic}")
        print("-" * 30)
        
        try:
            # Generate video using True RAG
            result = llm.generate_audio_first_video(topic)
            
            if result.get('success'):
                print(f"✅ Generation successful!")
                print(f"📝 Script length: {len(result['script'])} characters")
                print(f"🎵 Audio duration: {result['audio_timing'].get('total_duration', 'N/A')} seconds")
                print(f"🎬 Code length: {len(result['manim_code'])} characters")
                print(f"🧠 Context complexity: {result['context_analysis'].complexity_score:.2f}")
                print(f"🎯 Primary focus: {result['context_analysis'].primary_focus}")
                
                # Show some context analysis details
                math_concepts = result['context_analysis'].mathematical_concepts
                if math_concepts:
                    print(f"📊 Mathematical concepts detected: {len(math_concepts)}")
                    for concept in math_concepts[:3]:  # Show first 3
                        print(f"   - {concept['concept']} ({concept['category']}, {concept['complexity']})")
                
            else:
                print(f"❌ Generation failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
        
        print()

def demonstrate_context_analysis():
    """Demonstrate the context analysis capabilities"""
    
    print("\n🧠 Demonstrating Context Analysis")
    print("=" * 50)
    
    from app.true_rag_llm import ContextAnalyzer
    
    analyzer = ContextAnalyzer()
    
    # Test different types of prompts
    test_prompts = [
        {
            'prompt': 'explain derivatives visually',
            'script': 'Let\'s understand what a derivative really means. Think of it as the slope of a curve at any given point. We can visualize this using tangent lines that touch the curve at exactly one point.'
        },
        {
            'prompt': 'prove the pythagorean theorem',
            'script': 'The Pythagorean theorem states that in a right triangle, the square of the hypotenuse equals the sum of squares of the other two sides. Let\'s prove this rigorously using geometric reasoning.'
        },
        {
            'prompt': 'simple vector addition',
            'script': 'Vector addition is like walking in different directions. If you walk 3 steps east and then 4 steps north, you end up at a specific location. That\'s vector addition!'
        }
    ]
    
    for i, test in enumerate(test_prompts, 1):
        print(f"\n🎯 Analysis {i}: {test['prompt']}")
        print("-" * 30)
        
        try:
            context = analyzer.analyze_context(test['prompt'], test['script'])
            
            print(f"📊 Complexity Score: {context.complexity_score:.2f}")
            print(f"🎯 Primary Focus: {context.primary_focus}")
            print(f"📚 Mathematical Concepts: {len(context.mathematical_concepts)}")
            
            if context.mathematical_concepts:
                for concept in context.mathematical_concepts[:2]:
                    print(f"   - {concept['concept']} ({concept['category']})")
            
            print(f"⏱️ Temporal Context: {context.temporal_context.get('total_duration', 'N/A')}s")
            print(f"🎓 Educational Style: {context.educational_context.get('explanation_style', 'N/A')}")
            print(f"⚠️ Risk Factors: {len(context.risk_factors)}")
            
            if context.risk_factors:
                for risk in context.risk_factors[:2]:
                    print(f"   - {risk}")
                    
        except Exception as e:
            print(f"❌ Analysis failed: {e}")

def main():
    """Main function to run True RAG demonstrations"""
    
    print("🚀 True RAG Enhanced Manim System")
    print("=" * 60)
    print("This system demonstrates advanced RAG capabilities:")
    print("• Multi-dimensional context analysis")
    print("• Multi-source knowledge integration") 
    print("• Intelligent knowledge synthesis")
    print("• Adaptive learning capabilities")
    print("=" * 60)
    
    try:
        # Setup the True RAG system
        rag_system = setup_true_rag_system()
        
        # Demonstrate context analysis
        demonstrate_context_analysis()
        
        # Test True RAG generation
        test_true_rag_generation()
        
        print("\n🎉 True RAG demonstration completed!")
        print("The system is ready for advanced mathematical video generation.")
        
    except Exception as e:
        print(f"❌ Demonstration failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
