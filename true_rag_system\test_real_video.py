"""
Test script for generating real videos with the True RAG system
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_llm import TrueRAGEnhancedManimLLM

def test_real_video_generation():
    """Test real video generation with the True RAG system"""
    
    print("🎬 Testing True RAG Real Video Generation")
    print("=" * 60)
    
    # Initialize the True RAG Enhanced LLM
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    
    # Test topic
    topic = "derivatives and tangent lines"
    
    print(f"🎯 Generating video for: {topic}")
    print("-" * 40)
    
    try:
        # Generate using True RAG
        result = llm.generate_audio_first_video(topic)
        
        if result.get('success'):
            print(f"✅ True RAG generation successful!")
            
            # Display results
            print(f"\n📝 Generated Script:")
            print("-" * 20)
            print(result['script'][:300] + "..." if len(result['script']) > 300 else result['script'])
            
            print(f"\n🎬 Generated Manim Code:")
            print("-" * 20)
            print(result['manim_code'][:500] + "..." if len(result['manim_code']) > 500 else result['manim_code'])
            
            print(f"\n🧠 Context Analysis:")
            print("-" * 20)
            context = result['context_analysis']
            print(f"Complexity Score: {context.complexity_score:.2f}")
            print(f"Primary Focus: {context.primary_focus}")
            print(f"Mathematical Concepts: {len(context.mathematical_concepts)}")
            
            # Save the generated code to a file
            code_file = Path("generated_true_rag_code.py")
            with open(code_file, 'w') as f:
                f.write(result['manim_code'])
            
            print(f"\n💾 Code saved to: {code_file}")
            
            # Try to run the manim code (if manim is available)
            print(f"\n🎬 Attempting to generate video...")
            try:
                from app.manim_3b1b_runner import Manim3B1BRunner
                
                runner = Manim3B1BRunner()
                video_result = runner.run_manim_code(result['manim_code'], "TrueRAGScene")
                
                if video_result.get('success'):
                    print(f"✅ Video generation successful!")
                    print(f"📹 Video file: {video_result.get('video_file', 'Unknown')}")
                else:
                    print(f"❌ Video generation failed: {video_result.get('error', 'Unknown error')}")
                    
            except ImportError:
                print("⚠️ Manim runner not available, skipping video generation")
            except Exception as e:
                print(f"⚠️ Video generation error: {e}")
            
            return result
            
        else:
            print(f"❌ True RAG generation failed: {result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_multiple_topics():
    """Test multiple topics for comparison"""
    
    print("\n🎯 Testing Multiple Topics")
    print("=" * 60)
    
    topics = [
        "simple vector addition",
        "area under a curve", 
        "pythagorean theorem visualization"
    ]
    
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    
    results = []
    
    for i, topic in enumerate(topics, 1):
        print(f"\n🎬 Test {i}: {topic}")
        print("-" * 30)
        
        try:
            result = llm.generate_audio_first_video(topic)
            
            if result.get('success'):
                print(f"✅ Generation successful!")
                print(f"📊 Complexity: {result['context_analysis'].complexity_score:.2f}")
                print(f"🎯 Focus: {result['context_analysis'].primary_focus}")
                
                # Save code for each topic
                code_file = Path(f"generated_code_{topic.replace(' ', '_')}.py")
                with open(code_file, 'w') as f:
                    f.write(result['manim_code'])
                print(f"💾 Code saved to: {code_file}")
                
                results.append(result)
            else:
                print(f"❌ Generation failed: {result.get('error', 'Unknown')}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return results

def compare_with_current_system():
    """Compare True RAG with current system (if available)"""
    
    print("\n⚖️ Comparing True RAG vs Current System")
    print("=" * 60)
    
    topic = "derivatives and tangent lines"
    
    # Test True RAG system
    print("🧠 Testing True RAG System...")
    true_rag_llm = TrueRAGEnhancedManimLLM("true_rag_db")
    true_rag_result = true_rag_llm.generate_audio_first_video(topic)
    
    # Try to test current system
    print("\n📊 Testing Current System...")
    try:
        sys.path.append(str(Path(__file__).parent.parent / "app"))
        from llm_rag_audio_first import AudioFirstRAGEnhancedManimLLM
        
        current_llm = AudioFirstRAGEnhancedManimLLM()
        current_result = current_llm.generate_audio_first_video(topic)
        
        # Compare results
        print("\n📊 Comparison Results:")
        print("-" * 30)
        
        if true_rag_result.get('success') and current_result.get('success'):
            print("✅ Both systems generated successfully")
            
            print(f"\n📝 Script Lengths:")
            print(f"True RAG: {len(true_rag_result['script'])} characters")
            print(f"Current:  {len(current_result['script'])} characters")
            
            print(f"\n🎬 Code Lengths:")
            print(f"True RAG: {len(true_rag_result['manim_code'])} characters")
            print(f"Current:  {len(current_result['manim_code'])} characters")
            
            print(f"\n🧠 Context Analysis (True RAG only):")
            context = true_rag_result['context_analysis']
            print(f"Complexity: {context.complexity_score:.2f}")
            print(f"Focus: {context.primary_focus}")
            print(f"Concepts: {len(context.mathematical_concepts)}")
            
        else:
            print("⚠️ One or both systems failed to generate")
            
    except ImportError:
        print("⚠️ Current system not available for comparison")
    except Exception as e:
        print(f"⚠️ Comparison error: {e}")

def main():
    """Main test function"""
    
    print("🚀 True RAG Real Video Generation Test")
    print("=" * 70)
    
    # Test 1: Single video generation
    result = test_real_video_generation()
    
    # Test 2: Multiple topics
    if result:
        test_multiple_topics()
    
    # Test 3: Comparison with current system
    compare_with_current_system()
    
    print("\n🎉 True RAG real video testing completed!")

if __name__ == "__main__":
    main()
