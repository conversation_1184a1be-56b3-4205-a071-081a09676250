#!/usr/bin/env python3
"""
Setup script for RAG-Enhanced 3Blue1Brown Educational Video Generator
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is 3.11+"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 11):
        print("❌ Python 3.11+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True

def check_ffmpeg():
    """Check if FFmpeg is installed"""
    try:
        subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
        print("✅ FFmpeg is installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️ FFmpeg not found. Please install FFmpeg for audio/video processing")
        print("   Windows: winget install Gyan.FFmpeg")
        print("   Mac: brew install ffmpeg")
        print("   Linux: sudo apt install ffmpeg")
        return False

def install_dependencies():
    """Install Python dependencies"""
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )

def setup_environment():
    """Set up environment file"""
    env_file = Path(".env")
    if not env_file.exists():
        print("🔧 Creating .env file...")
        with open(env_file, "w") as f:
            f.write("# Add your OpenAI API key here\n")
            f.write("OPENAI_API_KEY=your_openai_api_key_here\n")
        print("✅ .env file created. Please add your OpenAI API key.")
        return False
    else:
        print("✅ .env file already exists")
        return True

def main():
    """Main setup function"""
    print("🚀 RAG-Enhanced 3Blue1Brown Generator Setup")
    print("=" * 50)
    
    # Check prerequisites
    if not check_python_version():
        sys.exit(1)
    
    ffmpeg_ok = check_ffmpeg()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Setup environment
    env_ok = setup_environment()
    
    print("\n" + "=" * 50)
    print("📋 Setup Summary:")
    print(f"✅ Python dependencies installed")
    print(f"{'✅' if ffmpeg_ok else '⚠️'} FFmpeg {'available' if ffmpeg_ok else 'needs installation'}")
    print(f"{'✅' if env_ok else '⚠️'} Environment {'configured' if env_ok else 'needs API key'}")
    
    print("\n🎯 Next Steps:")
    if not env_ok:
        print("1. Add your OpenAI API key to the .env file")
    if not ffmpeg_ok:
        print("2. Install FFmpeg for audio/video processing")
    print("3. Run: python setup_3b1b_rag.py (to build RAG database)")
    print("4. Run: python main_rag_3b1b.py (to generate videos)")
    
    print("\n📚 Documentation:")
    print("- README.md: Quick start guide")
    print("- instructions.txt: Detailed usage instructions")
    print("- working.txt: Technical documentation")

if __name__ == "__main__":
    main()
