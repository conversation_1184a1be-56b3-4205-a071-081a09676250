#!/usr/bin/env python3
"""
Setup script to implement RAG with 3Blue1Brown's manim codebase
This script will:
1. Clone 3b1b repositories
2. Set up vector database for RAG
3. Process and index manim code examples
4. Create retrieval system for enhanced LLM
"""

import os
import subprocess
import sys
from pathlib import Path
import requests
import zipfile
import shutil

def install_requirements():
    """Install required packages for RAG implementation"""
    requirements = [
        "sentence-transformers",  # For embeddings
        "chromadb",              # Vector database
        "langchain",             # RAG framework
        "langchain-community",   # Additional langchain tools
        "tiktoken",              # Token counting
        "gitpython",             # Git operations
    ]
    
    print("Installing RAG requirements...")
    for req in requirements:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
            print(f"✅ Installed {req}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {req}")
            return False
    return True

def clone_3b1b_repositories():
    """Clone 3Blue1Brown repositories"""
    repos = {
        "manim": "https://github.com/3b1b/manim.git",
        "videos": "https://github.com/3b1b/videos.git"
    }
    
    base_dir = Path("3b1b_data")
    base_dir.mkdir(exist_ok=True)
    
    for name, url in repos.items():
        repo_path = base_dir / name
        if repo_path.exists():
            print(f"📁 {name} repository already exists")
            continue
            
        print(f"🔄 Cloning {name} repository...")
        try:
            subprocess.check_call([
                "git", "clone", "--depth", "1", url, str(repo_path)
            ])
            print(f"✅ Cloned {name}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to clone {name}")
            return False
    
    return True

def setup_3b1b_manim():
    """Setup 3Blue1Brown's original manim"""
    manim_path = Path("3b1b_data/manim")
    
    if not manim_path.exists():
        print("❌ 3b1b manim not found. Run clone_3b1b_repositories() first.")
        return False
    
    print("🔧 Setting up 3Blue1Brown's manim...")
    
    # Install 3b1b manim in development mode
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-e", str(manim_path)
        ])
        print("✅ 3b1b manim installed")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install 3b1b manim")
        return False

def create_rag_system():
    """Create the RAG system for manim code"""
    print("🤖 Creating RAG system...")
    
    # This will be implemented in the next file
    rag_code = '''
from app.rag_system import ManimRAG

# Initialize RAG system
rag = ManimRAG()
rag.setup()
print("✅ RAG system created")
'''
    
    with open("setup_rag.py", "w", encoding="utf-8") as f:
        f.write(rag_code)
    
    return True

def main():
    """Main setup function"""
    print("🚀 Setting up 3Blue1Brown RAG-Enhanced Manim System")
    print("=" * 60)
    
    steps = [
        ("Installing requirements", install_requirements),
        ("Cloning 3b1b repositories", clone_3b1b_repositories),
        ("Setting up 3b1b manim", setup_3b1b_manim),
        ("Creating RAG system", create_rag_system),
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ Failed at: {step_name}")
            return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Run: python app/rag_system.py to build the knowledge base")
    print("2. Update your LLM to use RAG-enhanced prompts")
    print("3. Test with 3b1b manim examples")
    
    return True

if __name__ == "__main__":
    main()
