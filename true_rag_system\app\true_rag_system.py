"""
True RAG System Implementation

This module implements a comprehensive True RAG system with:
1. Multi-source knowledge bases
2. Context-aware retrieval
3. Intelligent knowledge synthesis
4. Adaptive learning capabilities
"""

import os
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import sqlite3
import pickle
from datetime import datetime
import hashlib

try:
    import chromadb
    from chromadb.config import Settings
except ImportError:
    print("ChromaDB not available. Install with: pip install chromadb")
    chromadb = None

@dataclass
class KnowledgeItem:
    """Individual knowledge item with metadata"""
    content: str
    metadata: Dict
    source: str
    relevance_score: float = 0.0
    confidence_score: float = 0.0
    context_tags: List[str] = None
    
    def __post_init__(self):
        if self.context_tags is None:
            self.context_tags = []

@dataclass
class QueryResult:
    """Result from knowledge retrieval"""
    items: List[KnowledgeItem]
    query_metadata: Dict
    retrieval_strategy: str
    total_results: int
    processing_time: float

class TrueRAGSystem:
    """
    Comprehensive True RAG system with multi-source knowledge integration
    """
    
    def __init__(self, db_path: str = "true_rag_db"):
        """Initialize the True RAG system"""
        self.db_path = Path(db_path)
        self.db_path.mkdir(exist_ok=True)
        
        # Initialize knowledge bases
        self.vector_db = None
        self.structured_db = None
        self.pattern_db = None
        self.solution_db = None
        
        # Initialize retrieval strategies
        self.retrieval_strategies = {}
        
        # Initialize learning components
        self.query_history = []
        self.performance_metrics = {}
        
        print(f"🧠 True RAG System initialized at {db_path}")
        
    def initialize_knowledge_bases(self):
        """Initialize all knowledge bases"""
        
        # Vector database for semantic similarity
        self._initialize_vector_db()
        
        # Structured database for relationships
        self._initialize_structured_db()
        
        # Pattern database for common patterns
        self._initialize_pattern_db()
        
        # Solution database for error handling
        self._initialize_solution_db()
        
        print("✅ All knowledge bases initialized")
    
    def _initialize_vector_db(self):
        """Initialize vector database for semantic search"""
        if chromadb is None:
            print("⚠️ ChromaDB not available, using fallback vector storage")
            self.vector_db = FallbackVectorDB(self.db_path / "vector_db")
            return
        
        try:
            self.vector_db = chromadb.PersistentClient(
                path=str(self.db_path / "chroma_db"),
                settings=Settings(anonymized_telemetry=False)
            )
            
            # Create collections for different types of content
            self.manim_collection = self.vector_db.get_or_create_collection(
                name="manim_examples",
                metadata={"description": "3Blue1Brown manim code examples"}
            )
            
            self.concept_collection = self.vector_db.get_or_create_collection(
                name="mathematical_concepts",
                metadata={"description": "Mathematical concept definitions and relationships"}
            )
            
            print("✅ Vector database initialized with ChromaDB")
            
        except Exception as e:
            print(f"⚠️ ChromaDB initialization failed: {e}")
            self.vector_db = FallbackVectorDB(self.db_path / "vector_db")
    
    def _initialize_structured_db(self):
        """Initialize structured database for relationships"""
        db_file = self.db_path / "structured_knowledge.db"
        
        self.structured_db = sqlite3.connect(str(db_file), check_same_thread=False)
        cursor = self.structured_db.cursor()
        
        # Create tables for different knowledge types
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mathematical_concepts (
                id INTEGER PRIMARY KEY,
                concept_name TEXT UNIQUE,
                category TEXT,
                difficulty_level INTEGER,
                prerequisites TEXT,
                visual_patterns TEXT,
                common_errors TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS concept_relationships (
                id INTEGER PRIMARY KEY,
                concept_a TEXT,
                concept_b TEXT,
                relationship_type TEXT,
                strength REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS animation_patterns (
                id INTEGER PRIMARY KEY,
                pattern_name TEXT,
                pattern_type TEXT,
                duration_range TEXT,
                complexity_level INTEGER,
                success_rate REAL,
                pattern_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        self.structured_db.commit()
        print("✅ Structured database initialized")
    
    def _initialize_pattern_db(self):
        """Initialize pattern database for common patterns"""
        pattern_file = self.db_path / "patterns.json"
        
        if pattern_file.exists():
            with open(pattern_file, 'r') as f:
                self.pattern_db = json.load(f)
        else:
            self.pattern_db = {
                'timing_patterns': {},
                'animation_patterns': {},
                'error_patterns': {},
                'success_patterns': {}
            }
            self._save_pattern_db()
        
        print("✅ Pattern database initialized")
    
    def _initialize_solution_db(self):
        """Initialize solution database for error handling"""
        solution_file = self.db_path / "solutions.json"
        
        if solution_file.exists():
            with open(solution_file, 'r') as f:
                self.solution_db = json.load(f)
        else:
            self.solution_db = {
                'error_solutions': {},
                'prevention_strategies': {},
                'fix_patterns': {},
                'validation_rules': {}
            }
            self._save_solution_db()
        
        print("✅ Solution database initialized")
    
    def add_manim_example(self, code: str, metadata: Dict):
        """Add a manim code example to the knowledge base"""

        # Add to vector database for semantic search
        if hasattr(self.vector_db, 'get_or_create_collection'):
            # ChromaDB - convert lists to strings for compatibility
            chroma_metadata = self._prepare_metadata_for_chroma(metadata)
            self.manim_collection.add(
                documents=[code],
                metadatas=[chroma_metadata],
                ids=[self._generate_id(code)]
            )
        else:
            # Fallback vector DB
            self.vector_db.add_document(code, metadata)

        # Extract and store patterns
        self._extract_and_store_patterns(code, metadata)

        print(f"✅ Added manim example: {metadata.get('title', 'Untitled')}")

    def _prepare_metadata_for_chroma(self, metadata: Dict) -> Dict:
        """Prepare metadata for ChromaDB by converting lists to strings"""
        chroma_metadata = {}

        for key, value in metadata.items():
            if isinstance(value, list):
                # Convert lists to comma-separated strings
                chroma_metadata[key] = ', '.join(str(item) for item in value)
            elif isinstance(value, (str, int, float, bool)) or value is None:
                # Keep supported types as-is
                chroma_metadata[key] = value
            else:
                # Convert other types to strings
                chroma_metadata[key] = str(value)

        return chroma_metadata
    
    def add_mathematical_concept(self, concept_name: str, concept_data: Dict):
        """Add a mathematical concept to the knowledge base"""
        
        cursor = self.structured_db.cursor()
        cursor.execute("""
            INSERT OR REPLACE INTO mathematical_concepts 
            (concept_name, category, difficulty_level, prerequisites, visual_patterns, common_errors)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            concept_name,
            concept_data.get('category', ''),
            concept_data.get('difficulty_level', 1),
            json.dumps(concept_data.get('prerequisites', [])),
            json.dumps(concept_data.get('visual_patterns', [])),
            json.dumps(concept_data.get('common_errors', []))
        ))
        
        self.structured_db.commit()
        print(f"✅ Added mathematical concept: {concept_name}")
    
    def query_knowledge(self, query: str, context: Dict, max_results: int = 10) -> QueryResult:
        """Query knowledge from multiple sources with context awareness"""
        
        start_time = datetime.now()
        
        # Determine optimal retrieval strategy
        strategy = self._determine_retrieval_strategy(query, context)
        
        # Retrieve from multiple sources
        results = []
        
        # Vector search for semantic similarity
        vector_results = self._query_vector_db(query, context, max_results // 2)
        results.extend(vector_results)
        
        # Structured search for relationships
        structured_results = self._query_structured_db(query, context, max_results // 4)
        results.extend(structured_results)
        
        # Pattern search for common patterns
        pattern_results = self._query_pattern_db(query, context, max_results // 4)
        results.extend(pattern_results)
        
        # Rank and filter results
        ranked_results = self._rank_and_filter_results(results, query, context)
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        # Store query for learning
        self._store_query_history(query, context, ranked_results, processing_time)
        
        return QueryResult(
            items=ranked_results[:max_results],
            query_metadata={'strategy': strategy, 'context': context},
            retrieval_strategy=strategy,
            total_results=len(results),
            processing_time=processing_time
        )
    
    def _query_vector_db(self, query: str, context: Dict, max_results: int) -> List[KnowledgeItem]:
        """Query vector database for semantic similarity"""
        results = []
        
        try:
            if hasattr(self.vector_db, 'get_or_create_collection'):
                # ChromaDB query
                query_results = self.manim_collection.query(
                    query_texts=[query],
                    n_results=max_results
                )
                
                for i, (doc, metadata) in enumerate(zip(
                    query_results['documents'][0],
                    query_results['metadatas'][0]
                )):
                    results.append(KnowledgeItem(
                        content=doc,
                        metadata=metadata,
                        source='vector_db',
                        relevance_score=1.0 - (i / max_results),  # Simple scoring
                        confidence_score=0.8
                    ))
            else:
                # Fallback vector DB query
                fallback_results = self.vector_db.query(query, max_results)
                for doc, metadata, score in fallback_results:
                    results.append(KnowledgeItem(
                        content=doc,
                        metadata=metadata,
                        source='vector_db',
                        relevance_score=score,
                        confidence_score=0.7
                    ))
                    
        except Exception as e:
            print(f"⚠️ Vector DB query failed: {e}")
        
        return results
    
    def _query_structured_db(self, query: str, context: Dict, max_results: int) -> List[KnowledgeItem]:
        """Query structured database for relationships"""
        results = []
        
        try:
            cursor = self.structured_db.cursor()
            
            # Search mathematical concepts
            cursor.execute("""
                SELECT concept_name, category, difficulty_level, prerequisites, visual_patterns
                FROM mathematical_concepts
                WHERE concept_name LIKE ? OR category LIKE ?
                LIMIT ?
            """, (f"%{query}%", f"%{query}%", max_results))
            
            for row in cursor.fetchall():
                concept_name, category, difficulty, prerequisites, visual_patterns = row
                
                results.append(KnowledgeItem(
                    content=f"Mathematical concept: {concept_name}",
                    metadata={
                        'concept_name': concept_name,
                        'category': category,
                        'difficulty_level': difficulty,
                        'prerequisites': json.loads(prerequisites or '[]'),
                        'visual_patterns': json.loads(visual_patterns or '[]')
                    },
                    source='structured_db',
                    relevance_score=0.8,
                    confidence_score=0.9
                ))
                
        except Exception as e:
            print(f"⚠️ Structured DB query failed: {e}")
        
        return results
    
    def _query_pattern_db(self, query: str, context: Dict, max_results: int) -> List[KnowledgeItem]:
        """Query pattern database for common patterns"""
        results = []
        
        try:
            # Search timing patterns
            for pattern_name, pattern_data in self.pattern_db.get('timing_patterns', {}).items():
                if query.lower() in pattern_name.lower():
                    results.append(KnowledgeItem(
                        content=f"Timing pattern: {pattern_name}",
                        metadata=pattern_data,
                        source='pattern_db',
                        relevance_score=0.7,
                        confidence_score=0.8
                    ))
            
            # Search animation patterns
            for pattern_name, pattern_data in self.pattern_db.get('animation_patterns', {}).items():
                if query.lower() in pattern_name.lower():
                    results.append(KnowledgeItem(
                        content=f"Animation pattern: {pattern_name}",
                        metadata=pattern_data,
                        source='pattern_db',
                        relevance_score=0.7,
                        confidence_score=0.8
                    ))
                    
        except Exception as e:
            print(f"⚠️ Pattern DB query failed: {e}")
        
        return results[:max_results]

    def _save_pattern_db(self):
        """Save pattern database to disk"""
        pattern_file = self.db_path / "patterns.json"
        with open(pattern_file, 'w') as f:
            json.dump(self.pattern_db, f, indent=2)

    def _save_solution_db(self):
        """Save solution database to disk"""
        solution_file = self.db_path / "solutions.json"
        with open(solution_file, 'w') as f:
            json.dump(self.solution_db, f, indent=2)

    def _generate_id(self, content: str) -> str:
        """Generate unique ID for content"""
        return hashlib.md5(content.encode()).hexdigest()[:8]

    def _extract_and_store_patterns(self, code: str, metadata: Dict):
        """Extract patterns from code and store them"""
        # Extract timing patterns
        duration = metadata.get('duration', 10)
        complexity = metadata.get('complexity', 'intermediate')

        pattern_key = f"{complexity}_{duration}s"
        if pattern_key not in self.pattern_db.get('timing_patterns', {}):
            self.pattern_db.setdefault('timing_patterns', {})[pattern_key] = {
                'duration': duration,
                'complexity': complexity,
                'success_count': 1,
                'code_examples': [metadata.get('title', 'Untitled')]
            }
        else:
            self.pattern_db['timing_patterns'][pattern_key]['success_count'] += 1
            self.pattern_db['timing_patterns'][pattern_key]['code_examples'].append(
                metadata.get('title', 'Untitled')
            )

    def _determine_retrieval_strategy(self, query: str, context: Dict) -> str:
        """Determine optimal retrieval strategy based on query and context"""
        # Simple strategy determination - can be enhanced
        if 'concept' in context:
            return 'concept_focused'
        elif 'timing' in context:
            return 'timing_focused'
        else:
            return 'balanced'

    def _rank_and_filter_results(self, results: List[KnowledgeItem], query: str, context: Dict) -> List[KnowledgeItem]:
        """Rank and filter results based on relevance and context"""
        # Simple ranking - can be enhanced with more sophisticated scoring
        query_words = set(query.lower().split())

        for item in results:
            # Boost score based on query word matches in content
            content_words = set(item.content.lower().split())
            word_overlap = len(query_words.intersection(content_words))
            item.relevance_score += word_overlap * 0.1

            # Boost score based on metadata matches
            if hasattr(item, 'metadata') and item.metadata:
                metadata_str = str(item.metadata).lower()
                metadata_overlap = len(query_words.intersection(set(metadata_str.split())))
                item.relevance_score += metadata_overlap * 0.05

        # Sort by relevance score
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results

    def _store_query_history(self, query: str, context: Dict, results: List[KnowledgeItem], processing_time: float):
        """Store query history for learning purposes"""
        self.query_history.append({
            'query': query,
            'context': context,
            'result_count': len(results),
            'processing_time': processing_time,
            'timestamp': datetime.now().isoformat()
        })

        # Keep only last 1000 queries
        if len(self.query_history) > 1000:
            self.query_history = self.query_history[-1000:]

class FallbackVectorDB:
    """Fallback vector database implementation when ChromaDB is not available"""
    
    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.db_path.mkdir(exist_ok=True)
        self.documents = []
        self.load_documents()
    
    def add_document(self, content: str, metadata: Dict):
        """Add document to fallback vector DB"""
        self.documents.append({
            'content': content,
            'metadata': metadata,
            'id': self._generate_id(content)
        })
        self.save_documents()
    
    def query(self, query: str, max_results: int) -> List[Tuple[str, Dict, float]]:
        """Simple text-based query for fallback"""
        results = []
        query_lower = query.lower()
        
        for doc in self.documents:
            content = doc['content'].lower()
            # Simple relevance scoring based on keyword matches
            score = sum(1 for word in query_lower.split() if word in content) / len(query_lower.split())
            
            if score > 0:
                results.append((doc['content'], doc['metadata'], score))
        
        # Sort by score and return top results
        results.sort(key=lambda x: x[2], reverse=True)
        return results[:max_results]
    
    def load_documents(self):
        """Load documents from disk"""
        doc_file = self.db_path / "documents.pkl"
        if doc_file.exists():
            with open(doc_file, 'rb') as f:
                self.documents = pickle.load(f)
    
    def save_documents(self):
        """Save documents to disk"""
        doc_file = self.db_path / "documents.pkl"
        with open(doc_file, 'wb') as f:
            pickle.dump(self.documents, f)
    
    def _generate_id(self, content: str) -> str:
        """Generate unique ID for content"""
        return hashlib.md5(content.encode()).hexdigest()[:8]
