"""
Complete True RAG Enhanced Manim System
Mirrors the original workflow: Script → Audio → Video → Merge with timing
"""

import os
import sys
import json
import uuid
from pathlib import Path
from typing import Dict, Any, Optional

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_llm import TrueRAGEnhancedManimLLM
from app.synchronized_tts import SynchronizedTTS
from app.manim_3b1b_runner import generate_video_3b1b
from app.video_merger import merge_audio_video

class CompleteTrueRAGSystem:
    """Complete True RAG system with full workflow"""
    
    def __init__(self, rag_db_path: str = "true_rag_db"):
        """Initialize the complete True RAG system"""
        
        print("🧠 Initializing Complete True RAG System")
        print("=" * 60)
        
        # Initialize components
        self.llm = TrueRAGEnhancedManimLLM(rag_db_path)
        self.tts = SynchronizedTTS()
        
        # Create output directories
        self.output_dir = Path("true_rag_output")
        self.output_dir.mkdir(exist_ok=True)
        
        print("✅ True RAG system initialized successfully")
    
    def generate_complete_video(self, topic: str, output_format: str = "mp4") -> Dict[str, Any]:
        """
        Generate complete video with True RAG system
        
        Args:
            topic: The educational topic to generate
            output_format: Output video format (mp4 or gif)
            
        Returns:
            Dict with all generated files and metadata
        """
        
        print(f"\n🚀 Starting Complete True RAG Video Generation")
        print(f"🎯 Topic: {topic}")
        print(f"📹 Format: {output_format}")
        print("=" * 70)
        
        # Generate unique job ID
        job_id = f"true_rag_{uuid.uuid4().hex[:8]}"
        job_dir = self.output_dir / job_id
        job_dir.mkdir(exist_ok=True)
        
        try:
            # Step 1: Generate educational script with True RAG
            print("📝 Step 1: Generating True RAG Enhanced Script")
            print("-" * 50)
            
            script_result = self._generate_enhanced_script(topic)
            if not script_result['success']:
                return {'success': False, 'error': f"Script generation failed: {script_result['error']}"}
            
            script = script_result['script']
            context_analysis = script_result['context_analysis']
            
            print(f"✅ Script generated ({len(script)} characters)")
            print(f"🧠 Context: {context_analysis.primary_focus}, complexity: {context_analysis.complexity_score:.2f}")
            
            # Save script
            script_file = job_dir / "script.txt"
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script)
            
            # Step 2: Generate synchronized audio
            print(f"\n🎵 Step 2: Generating Synchronized Audio")
            print("-" * 50)
            
            audio_result = self._generate_synchronized_audio(script, job_dir)
            if not audio_result['success']:
                return {'success': False, 'error': f"Audio generation failed: {audio_result['error']}"}
            
            audio_file = audio_result['audio_file']
            timing_info = audio_result['timing_info']
            
            print(f"✅ Audio generated: {audio_file}")
            print(f"⏱️ Duration: {timing_info['total_duration']:.2f} seconds")
            
            # Step 3: Generate context-aware manim video
            print(f"\n🎬 Step 3: Generating Context-Aware Manim Video")
            print("-" * 50)
            
            video_result = self._generate_context_aware_video(
                topic, script, timing_info, context_analysis, job_id
            )
            if not video_result['success']:
                return {'success': False, 'error': f"Video generation failed: {video_result['error']}"}
            
            video_file = video_result['video_file']
            manim_code = video_result['manim_code']
            
            print(f"✅ Video generated: {video_file}")
            
            # Save manim code
            code_file = job_dir / "manim_code.py"
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(manim_code)
            
            # Step 4: Merge audio and video with timing synchronization
            print(f"\n🔄 Step 4: Merging Audio and Video with Timing Sync")
            print("-" * 50)
            
            merge_result = self._merge_with_timing_sync(
                video_file, audio_file, timing_info, job_dir, output_format
            )
            if not merge_result['success']:
                return {'success': False, 'error': f"Merge failed: {merge_result['error']}"}
            
            final_video = merge_result['final_video']
            
            print(f"✅ Final video created: {final_video}")
            
            # Step 5: Generate metadata and summary
            metadata = self._generate_metadata(
                topic, script, context_analysis, timing_info, 
                script_file, audio_file, video_file, final_video, manim_code
            )
            
            # Save metadata
            metadata_file = job_dir / "metadata.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            print(f"\n🎉 Complete True RAG Video Generation Successful!")
            print(f"📁 Job Directory: {job_dir}")
            print(f"📹 Final Video: {final_video}")
            print(f"📊 Metadata: {metadata_file}")
            
            return {
                'success': True,
                'job_id': job_id,
                'final_video': str(final_video),
                'script_file': str(script_file),
                'audio_file': str(audio_file),
                'video_file': str(video_file),
                'code_file': str(code_file),
                'metadata_file': str(metadata_file),
                'metadata': metadata
            }
            
        except Exception as e:
            print(f"❌ Complete generation failed: {e}")
            import traceback
            traceback.print_exc()
            return {'success': False, 'error': str(e)}
    
    def _generate_enhanced_script(self, topic: str) -> Dict[str, Any]:
        """Generate enhanced script using True RAG"""
        
        try:
            # Use the True RAG LLM to generate script with context analysis
            result = self.llm.generate_audio_first_video(topic)
            
            if result.get('success'):
                return {
                    'success': True,
                    'script': result['script'],
                    'context_analysis': result['context_analysis'],
                    'knowledge_synthesis': result.get('knowledge_synthesis', {})
                }
            else:
                return {'success': False, 'error': result.get('error', 'Unknown error')}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _generate_synchronized_audio(self, script: str, job_dir: Path) -> Dict[str, Any]:
        """Generate synchronized audio with timing information"""

        try:
            # Generate unique job ID for audio
            audio_job_id = f"audio_{uuid.uuid4().hex[:8]}"

            # Use SynchronizedTTS to create audio with timing
            audio_file = self.tts.generate_timed_audio(script, audio_job_id)

            if audio_file and Path(audio_file).exists():
                # Move audio file to job directory
                target_audio = job_dir / "audio.mp3"
                import shutil
                shutil.move(audio_file, target_audio)

                # Create basic timing info (the TTS system handles internal timing)
                words = len(script.split())
                estimated_duration = (words / 150) * 60  # 150 words per minute

                timing_info = {
                    'total_duration': estimated_duration,
                    'segments': [
                        {'start': 0, 'end': estimated_duration, 'text': script}
                    ]
                }

                return {
                    'success': True,
                    'audio_file': str(target_audio),
                    'timing_info': timing_info
                }
            else:
                return {'success': False, 'error': 'Audio file not generated'}

        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _generate_context_aware_video(self, topic: str, script: str, timing_info: Dict, 
                                    context_analysis, job_id: str) -> Dict[str, Any]:
        """Generate context-aware manim video"""
        
        try:
            # Generate manim code using True RAG with timing context
            manim_code = self.llm.generate_synchronized_manim_code(
                topic, script, timing_info, context_analysis, {}
            )
            
            # Generate video using 3Blue1Brown manim
            video_file = generate_video_3b1b(
                manim_code=manim_code,
                job_id=job_id,
                scene_name="TrueRAGScene",
                output_format="mp4"
            )
            
            if video_file and Path(video_file).exists():
                return {
                    'success': True,
                    'video_file': video_file,
                    'manim_code': manim_code
                }
            else:
                return {'success': False, 'error': 'Video file not generated'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _merge_with_timing_sync(self, video_file: str, audio_file: str,
                              timing_info: Dict, job_dir: Path, output_format: str) -> Dict[str, Any]:
        """Merge video and audio with timing synchronization"""

        try:
            # Generate unique job ID for merge
            merge_job_id = f"merge_{uuid.uuid4().hex[:8]}"

            # Use the existing merge_audio_video function
            final_video = merge_audio_video(
                audio_path=audio_file,
                video_path=video_file,
                job_id=merge_job_id,
                output_name="final_video"
            )

            if final_video and Path(final_video).exists():
                # Move final video to job directory
                target_final = job_dir / f"final_video.{output_format}"
                import shutil
                shutil.move(final_video, target_final)

                return {
                    'success': True,
                    'final_video': str(target_final)
                }
            else:
                return {'success': False, 'error': 'Final video not generated'}

        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _generate_metadata(self, topic: str, script: str, context_analysis, timing_info: Dict,
                         script_file: Path, audio_file: str, video_file: str, 
                         final_video: Path, manim_code: str) -> Dict[str, Any]:
        """Generate comprehensive metadata"""
        
        return {
            'topic': topic,
            'generation_info': {
                'system': 'True RAG Enhanced Manim System',
                'version': '1.0',
                'timestamp': str(Path().cwd()),
                'primary_focus': context_analysis.primary_focus,
                'complexity_score': context_analysis.complexity_score,
                'mathematical_concepts': len(context_analysis.mathematical_concepts)
            },
            'content_info': {
                'script_length': len(script),
                'script_words': len(script.split()),
                'audio_duration': timing_info.get('total_duration', 0),
                'code_lines': len(manim_code.split('\n'))
            },
            'files': {
                'script': str(script_file),
                'audio': audio_file,
                'video': video_file,
                'final_video': str(final_video),
                'code': str(script_file.parent / "manim_code.py")
            },
            'context_analysis': {
                'primary_focus': context_analysis.primary_focus,
                'complexity_score': context_analysis.complexity_score,
                'mathematical_concepts': [
                    {
                        'concept': concept.get('concept', ''),
                        'category': concept.get('category', ''),
                        'complexity': concept.get('complexity', '')
                    }
                    for concept in context_analysis.mathematical_concepts[:5]  # Top 5
                ]
            },
            'timing_info': timing_info
        }

def test_complete_system():
    """Test the complete True RAG system"""
    
    print("🧪 Testing Complete True RAG System")
    print("=" * 70)
    
    # Initialize system
    system = CompleteTrueRAGSystem()
    
    # Test topics
    test_topics = [
        "derivatives and tangent lines",
        "vector addition in 2D",
        "area under a curve"
    ]
    
    results = []
    
    for i, topic in enumerate(test_topics, 1):
        print(f"\n🎯 Test {i}: {topic}")
        print("=" * 50)
        
        try:
            result = system.generate_complete_video(topic, output_format="mp4")
            
            if result['success']:
                print(f"✅ Complete generation successful!")
                print(f"📹 Final video: {result['final_video']}")
                print(f"📊 Metadata: {result['metadata_file']}")
                results.append(result)
            else:
                print(f"❌ Generation failed: {result['error']}")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    # Summary
    print(f"\n🎉 Complete System Test Summary")
    print("=" * 50)
    print(f"✅ Successful generations: {len(results)}/{len(test_topics)}")
    
    for result in results:
        metadata = result['metadata']
        print(f"\n📹 {metadata['topic']}:")
        print(f"   - Duration: {metadata['timing_info'].get('total_duration', 0):.1f}s")
        print(f"   - Complexity: {metadata['context_analysis']['complexity_score']:.2f}")
        print(f"   - Focus: {metadata['context_analysis']['primary_focus']}")
        print(f"   - Final video: {result['final_video']}")
    
    return results

if __name__ == "__main__":
    test_complete_system()
