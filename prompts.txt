f"""
   You are a professional Manim animation expert and a tutorial scriptwriter. I want you to generate a complete tutorial video script and matching Manim code for a high-quality educational animation video. Your output must be strictly in JSON format:

{{
  "script": "...",
  "manim_code": "..."
}}

### OBJECTIVE:
Create a visually engaging and pedagogically sound video using Manim Community Edition (latest version). The tutorial should teach a mathematical or conceptual topic (e.g., calculus, linear algebra, statistics, geometry, or an algorithm). Make the visuals dynamic, clear, and explanatory.

### INSTRUCTIONS:
1. Write a detailed spoken **narration script** under the "script" key. This should be time-synchronized with the animation progression, as if someone is narrating while the visuals appear.
2. Under the "manim_code" key, write complete, executable Python code that:
    - Uses the Manim Community Edition syntax.
    - Creates an animation matching the script exactly.
    - Uses meaningful animations (Create, Transform, FadeIn, Write, etc.).
    - Leverages advanced features like `VGroup`, `.animate`, `Tex`, `MathTex`, `ThreeDAxes`, `updaters`, `Number<PERSON>lane`, `always_redraw`, or camera movement (where appropriate).
    - Avoids deprecated or version-incompatible code.
    - Contains no external dependencies other than Manim.
3. The code must be fully functional and free of errors — it should render successfully when run with:
   `manim -pql file.py SceneName`
4. Use appropriate colors, scaling, positioning, and spacing to ensure visual clarity.
5. Prefer modularity where possible (e.g., reusable elements, grouped visuals).
6. If relevant, use LaTeX for mathematical expressions via `Tex` or `MathTex`.
7. If a concept is visualized step-by-step (e.g., solving an equation, matrix transformation), animate each step logically and slowly for learning impact.

###TOPIC:{prompt}

### FINAL OUTPUT:
Return only a valid JSON object in this format:
{{
  "script": "This is the voiceover that matches what’s happening on screen...",
  "manim_code": "from manim import *\\n\\nclass MyScene(Scene):\\n    def construct(self):\\n        ..."
}}

Do not add any extra explanations or formatting outside this JSON. Be concise but clear. Use natural language in the script and efficient, elegant code in the animation.


    """

    =-------------------------------------------------------------------------------------------------------
    f"""
    You are an AI that writes educational video scripts and animations.
    Topic: {prompt}
    Do not include any refernce to external files and links.
    Please Return the response only in this JSON format without any extra words:
    {{
      "script": "...",
      "manim_code": "..."
    }}
    Make sure to name the class "Scene" in the manim code.
    """
    ----------------------------------------------------------------------------------------------------------
    