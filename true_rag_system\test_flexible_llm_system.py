"""
Test the flexible LLM-driven True RAG system
This system can handle ANY topic, not just predefined ones
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from complete_true_rag_system import CompleteTrueRAGSystem

def test_flexible_topics():
    """Test the system with diverse topics to prove flexibility"""
    
    print("🚀 Testing Flexible LLM-Driven True RAG System")
    print("=" * 80)
    print("Testing with diverse topics to prove the system works for ANY topic")
    print("=" * 80)
    
    # Initialize system
    system = CompleteTrueRAGSystem()
    
    # Test diverse topics - including ones NOT in the original hardcoded system
    test_topics = [
        "quadratic equations and parabolas",  # Math topic
        "probability and dice rolling",       # Statistics topic  
        "geometric transformations",          # Geometry topic
        "exponential growth and decay",       # Applied math topic
        "trigonometric functions",            # Trigonometry topic
    ]
    
    results = []
    
    for i, topic in enumerate(test_topics, 1):
        print(f"\n🎯 Test {i}: {topic}")
        print("=" * 60)
        print(f"Testing if LLM can generate code for: {topic}")
        print("-" * 60)
        
        try:
            result = system.generate_complete_video(topic, output_format="mp4")
            
            if result['success']:
                print(f"✅ SUCCESS: LLM generated code for '{topic}'")
                
                # Analyze the generated content
                with open(result['script_file'], 'r', encoding='utf-8') as f:
                    script = f.read()
                with open(result['code_file'], 'r', encoding='utf-8') as f:
                    code = f.read()
                
                # Check if content is topic-specific
                topic_words = topic.lower().split()
                script_lower = script.lower()
                code_lower = code.lower()
                
                topic_in_script = sum(1 for word in topic_words if word in script_lower)
                topic_in_code = sum(1 for word in topic_words if word in code_lower)
                
                print(f"📝 Script relevance: {topic_in_script}/{len(topic_words)} topic words found")
                print(f"🎬 Code relevance: {topic_in_code}/{len(topic_words)} topic words found")
                
                # Check for script synchronization
                script_sentences = len([s.strip() for s in script.split('.') if s.strip()])
                code_markers = len([line for line in code.split('\n') if 'Sentence' in line])
                
                print(f"🔄 Synchronization: {code_markers} markers for {script_sentences} sentences")
                
                if topic_in_script > 0 or topic_in_code > 0:
                    print(f"✅ Content is topic-specific!")
                else:
                    print(f"⚠️ Content may be generic")
                
                print(f"📹 Video: {result['final_video']}")
                results.append(result)
                
            else:
                print(f"❌ FAILED: {result['error']}")
                
        except Exception as e:
            print(f"❌ ERROR: {e}")
            import traceback
            traceback.print_exc()
    
    return results

def analyze_flexibility(results):
    """Analyze how flexible the system is"""
    
    print(f"\n🔍 Flexibility Analysis")
    print("=" * 50)
    
    if not results:
        print("❌ No successful results to analyze")
        return
    
    print(f"✅ Successfully handled {len(results)} diverse topics")
    
    # Analyze code diversity
    all_codes = []
    for result in results:
        with open(result['code_file'], 'r', encoding='utf-8') as f:
            all_codes.append(f.read())
    
    # Check for code diversity (different mathematical objects used)
    math_objects = ['Arrow', 'Axes', 'ParametricFunction', 'Circle', 'Rectangle', 
                   'Line', 'Dot', 'VGroup', 'NumberPlane', 'Graph']
    
    object_usage = {}
    for obj in math_objects:
        object_usage[obj] = sum(1 for code in all_codes if obj in code)
    
    print(f"\n📊 Mathematical Object Usage:")
    for obj, count in object_usage.items():
        if count > 0:
            print(f"   {obj}: used in {count}/{len(results)} videos")
    
    # Check for different animation types
    animation_types = ['ShowCreation', 'Write', 'Transform', 'FadeIn', 'FadeOut', 'MoveToTarget']
    
    animation_usage = {}
    for anim in animation_types:
        animation_usage[anim] = sum(1 for code in all_codes if anim in code)
    
    print(f"\n🎬 Animation Type Usage:")
    for anim, count in animation_usage.items():
        if count > 0:
            print(f"   {anim}: used in {count}/{len(results)} videos")
    
    # Calculate diversity score
    unique_objects = len([obj for obj, count in object_usage.items() if count > 0])
    unique_animations = len([anim for anim, count in animation_usage.items() if count > 0])
    
    diversity_score = (unique_objects + unique_animations) / (len(math_objects) + len(animation_types))
    
    print(f"\n🎯 Diversity Score: {diversity_score:.2f}")
    
    if diversity_score > 0.5:
        print("✅ HIGH DIVERSITY: System generates varied content")
    elif diversity_score > 0.3:
        print("👍 MODERATE DIVERSITY: System shows some variation")
    else:
        print("⚠️ LOW DIVERSITY: System may be too generic")

def test_novel_topic():
    """Test with a completely novel topic not in training data"""
    
    print(f"\n🆕 Testing Novel Topic")
    print("=" * 50)
    print("Testing with a topic that definitely wasn't in the hardcoded system")
    
    system = CompleteTrueRAGSystem()
    
    # A very specific, novel topic
    novel_topic = "Fibonacci sequence and golden ratio visualization"
    
    print(f"🎯 Novel topic: {novel_topic}")
    
    try:
        result = system.generate_complete_video(novel_topic, output_format="mp4")
        
        if result['success']:
            print(f"✅ SUCCESS: LLM handled novel topic!")
            
            # Check content
            with open(result['script_file'], 'r', encoding='utf-8') as f:
                script = f.read()
            with open(result['code_file'], 'r', encoding='utf-8') as f:
                code = f.read()
            
            # Look for Fibonacci/golden ratio specific content
            fibonacci_terms = ['fibonacci', 'golden', 'ratio', 'spiral', 'sequence']
            script_lower = script.lower()
            code_lower = code.lower()
            
            found_terms = [term for term in fibonacci_terms if term in script_lower or term in code_lower]
            
            print(f"📝 Found Fibonacci-related terms: {found_terms}")
            
            if found_terms:
                print(f"✅ EXCELLENT: Content is specifically about Fibonacci/golden ratio!")
            else:
                print(f"⚠️ Content may be generic mathematical visualization")
            
            print(f"📹 Video: {result['final_video']}")
            return result
        else:
            print(f"❌ FAILED: {result['error']}")
            return None
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return None

def main():
    """Main test function"""
    
    print("🧪 Testing Flexible LLM-Driven True RAG System")
    print("=" * 80)
    print("This test proves the system works for ANY topic, not just predefined ones")
    print("=" * 80)
    
    # Test 1: Multiple diverse topics
    results = test_flexible_topics()
    
    # Test 2: Analyze flexibility
    if results:
        analyze_flexibility(results)
    
    # Test 3: Novel topic test
    novel_result = test_novel_topic()
    
    # Summary
    print(f"\n🎉 Flexible System Test Summary")
    print("=" * 60)
    
    total_tests = len([
        "quadratic equations and parabolas",
        "probability and dice rolling", 
        "geometric transformations",
        "exponential growth and decay",
        "trigonometric functions"
    ])
    
    print(f"✅ Diverse topics tested: {len(results)}/{total_tests}")
    
    if novel_result:
        print(f"✅ Novel topic test: SUCCESS")
    else:
        print(f"❌ Novel topic test: FAILED")
    
    if len(results) >= 3:
        print(f"\n🎯 KEY ACHIEVEMENTS:")
        print(f"✅ LLM generates code dynamically for any topic")
        print(f"✅ RAG provides examples and patterns as guidance")
        print(f"✅ No hardcoded topic restrictions")
        print(f"✅ Script-synchronized video generation")
        print(f"✅ Flexible and extensible system")
        print(f"\n🚀 The True RAG system is now truly flexible and can handle ANY mathematical topic!")
    else:
        print(f"\n⚠️ System needs improvement for better topic flexibility")

if __name__ == "__main__":
    main()
