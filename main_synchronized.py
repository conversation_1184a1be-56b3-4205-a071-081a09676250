"""
Synchronized 3Blue1Brown Video Generator
Complete pipeline with audio-video synchronization
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv

# Import our synchronized modules
from app.synchronized_generator import generate_synchronized_video
from app.synchronized_tts import generate_synchronized_audio
from app.manim_3b1b_runner import generate_video_3b1b, check_3b1b_manim_installation
from app.video_merger import merge_audio_video

load_dotenv()


def generate_synchronized_3b1b_video(prompt: str, job_id: str = None, 
                                   target_duration: float = 30.0, 
                                   output_format: str = "mp4") -> dict:
    """
    Generate a fully synchronized 3Blue1Brown-style educational video
    
    Args:
        prompt: Educational topic to visualize
        job_id: Unique identifier (auto-generated if None)
        target_duration: Target video duration in seconds
        output_format: Output format ("mp4" or "gif")
    
    Returns:
        Dictionary with paths to generated files and metadata
    """
    
    # Generate job ID if not provided
    if job_id is None:
        import uuid
        job_id = str(uuid.uuid4())[:8]
    
    print("🎬 Synchronized 3Blue1Brown Video Generator")
    print("=" * 60)
    print(f"📝 Topic: {prompt}")
    print(f"🆔 Job ID: {job_id}")
    print(f"⏱️ Target Duration: {target_duration} seconds")
    print(f"📹 Format: {output_format.upper()}")
    print()
    
    # Check 3b1b manim installation
    print("🔍 Checking 3Blue1Brown manim installation...")
    if not check_3b1b_manim_installation():
        print("❌ 3Blue1Brown manim not found. Please run setup_3b1b_rag.py first.")
        return {"success": False, "error": "3b1b manim not installed"}
    print("✅ 3Blue1Brown manim found")
    print()
    
    results = {
        "job_id": job_id,
        "prompt": prompt,
        "target_duration": target_duration,
        "success": False,
        "files": {},
        "timing": {}
    }
    
    try:
        # Step 1: Generate synchronized script and manim code
        print("🤖 Step 1: Generating synchronized script and manim code...")
        print("-" * 50)
        
        content_result = generate_synchronized_video(prompt, job_id, target_duration)
        
        print(f"✅ Content generated successfully!")
        print(f"📊 Estimated duration: {content_result.get('estimated_duration', 'Unknown')}s")
        print(f"📊 Speech duration: {content_result.get('actual_speech_duration', 'Unknown')}s")
        print()
        
        results["timing"]["estimated_duration"] = content_result.get("estimated_duration")
        results["timing"]["speech_duration"] = content_result.get("actual_speech_duration")
        results["files"]["script"] = f"generated/{job_id}/script.txt"
        results["files"]["manim_code"] = f"generated/{job_id}/manim_code.py"
        
        # Step 2: Generate synchronized audio
        print("🎵 Step 2: Generating synchronized audio...")
        print("-" * 50)
        
        audio_path = generate_synchronized_audio(
            content_result["script"], 
            job_id, 
            timing_info=content_result.get("timing_segments"),
            target_duration=target_duration
        )
        
        if audio_path:
            print(f"✅ Audio generated: {audio_path}")
            results["files"]["audio"] = audio_path
        else:
            print("❌ Audio generation failed")
            return results
        print()
        
        # Step 3: Generate video with 3b1b manim
        print(f"🎬 Step 3: Generating {output_format.upper()} with 3Blue1Brown manim...")
        print("-" * 50)
        
        video_path = generate_video_3b1b(
            content_result["manim_code"], 
            job_id, 
            output_format=output_format
        )
        
        if video_path:
            print(f"✅ Video generated: {video_path}")
            results["files"]["video"] = video_path
        else:
            print("❌ Video generation failed")
            return results
        print()
        
        # Step 4: Merge audio and video
        print("🔗 Step 4: Merging audio and video...")
        print("-" * 50)
        
        try:
            final_video_path = merge_audio_video(
                audio_path, 
                video_path, 
                job_id,
                output_name="synchronized_video"
            )
            
            print(f"✅ Final video created: {final_video_path}")
            results["files"]["final_video"] = final_video_path
            results["success"] = True
            
        except Exception as e:
            print(f"⚠️ Video merging failed: {e}")
            print("📁 Separate audio and video files are available")
            # Still consider it a success since we have both files
            results["success"] = True
        
        print()
        
        # Step 5: Generate summary report
        print("📋 Step 5: Generating summary report...")
        print("-" * 50)
        
        summary = generate_summary_report(results, content_result)
        summary_path = f"generated/{job_id}/summary_report.json"
        
        with open(summary_path, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2)
        
        results["files"]["summary"] = summary_path
        print(f"✅ Summary report saved: {summary_path}")
        print()
        
        # Final summary
        print("🎉 GENERATION COMPLETE!")
        print("=" * 60)
        print(f"📁 All files saved in: generated/{job_id}/")
        print(f"🎬 Final video: {results['files'].get('final_video', 'Not merged')}")
        print(f"🎵 Audio: {results['files'].get('audio', 'Not generated')}")
        print(f"📹 Video: {results['files'].get('video', 'Not generated')}")
        print(f"📝 Script: {results['files'].get('script', 'Not generated')}")
        print(f"🐍 Code: {results['files'].get('manim_code', 'Not generated')}")
        
        return results
        
    except Exception as e:
        print(f"❌ Generation failed: {e}")
        results["error"] = str(e)
        return results


def generate_summary_report(results: dict, content_result: dict) -> dict:
    """Generate a comprehensive summary report"""
    
    summary = {
        "generation_info": {
            "job_id": results["job_id"],
            "prompt": results["prompt"],
            "target_duration": results["target_duration"],
            "success": results["success"],
            "timestamp": str(Path(f"generated/{results['job_id']}").stat().st_mtime)
        },
        "timing_analysis": {
            "estimated_duration": content_result.get("estimated_duration"),
            "actual_speech_duration": content_result.get("actual_speech_duration"),
            "timing_segments": content_result.get("timing_segments", [])
        },
        "files_generated": results["files"],
        "rag_enhanced": content_result.get("rag_enhanced", False)
    }
    
    # Add file size information
    for file_type, file_path in results["files"].items():
        if file_path and Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            summary["files_generated"][f"{file_type}_size_mb"] = round(file_size / (1024 * 1024), 2)
    
    return summary


def main():
    """Interactive main function"""
    
    print("🎬 Synchronized 3Blue1Brown Video Generator")
    print("=" * 60)
    print()
    
    # Get user input
    prompt = input("Enter your mathematical topic: ").strip()
    if not prompt:
        prompt = "visualizing the derivative as the slope of a tangent line"
        print(f"Using default topic: {prompt}")
    
    # Get target duration
    duration_input = input("Target duration in seconds (default: 30): ").strip()
    try:
        target_duration = float(duration_input) if duration_input else 30.0
    except ValueError:
        target_duration = 30.0
        print(f"Invalid duration, using default: {target_duration}s")
    
    # Get output format
    format_input = input("Output format (mp4/gif, default: mp4): ").strip().lower()
    output_format = format_input if format_input in ["mp4", "gif"] else "mp4"
    
    print()
    
    # Generate video
    results = generate_synchronized_3b1b_video(
        prompt=prompt,
        target_duration=target_duration,
        output_format=output_format
    )
    
    if results["success"]:
        print("\n🎯 Next Steps:")
        print("1. Review the generated video and audio")
        print("2. Check the timing analysis in the summary report")
        print("3. Iterate and improve based on results")
    else:
        print(f"\n❌ Generation failed: {results.get('error', 'Unknown error')}")


if __name__ == "__main__":
    main()
