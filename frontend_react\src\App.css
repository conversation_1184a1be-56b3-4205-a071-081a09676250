.App {
  min-height: 100vh;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Tab Navigation */
.tab-nav {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: white;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.tab-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.tab-button.active {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Tab Content */
.tab-content {
  min-height: 500px;
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 20px;
}

/* About Section */
.about-section h2 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 28px;
}

.about-section h3 {
  color: #34495e;
  margin: 25px 0 15px 0;
  font-size: 20px;
}

.about-section h4 {
  color: #34495e;
  margin: 20px 0 10px 0;
  font-size: 16px;
}

.about-section p {
  line-height: 1.6;
  margin-bottom: 15px;
  color: #555;
}

.about-section ul {
  margin-left: 20px;
  margin-bottom: 20px;
}

.about-section li {
  margin-bottom: 8px;
  line-height: 1.5;
  color: #555;
}

.about-section ol {
  margin-left: 20px;
  margin-bottom: 20px;
}

.about-section ol li {
  margin-bottom: 10px;
  line-height: 1.5;
  color: #555;
}

.tech-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-top: 25px;
  border-left: 4px solid #667eea;
}

.tech-info p {
  margin: 0;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }
  
  .tab-nav {
    flex-direction: column;
    gap: 5px;
  }
  
  .tab-button {
    justify-content: center;
    padding: 15px;
  }
  
  .card {
    padding: 20px;
  }
  
  .about-section h2 {
    font-size: 24px;
  }
  
  .about-section h3 {
    font-size: 18px;
  }
}
