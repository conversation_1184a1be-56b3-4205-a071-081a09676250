import os
import json
import re
from pathlib import Path
from typing import Dict, List, Tuple
import google.generativeai as genai
from dotenv import load_dotenv

from .rag_system import ManimRAG
from .synchronized_tts import SynchronizedTTS
from .tts import extract_sentences

load_dotenv()

primary_key = os.getenv("GOOGLE_API_KEY2")
backup_key = os.getenv("GOOGLE_API_KEY")
current_api_key = primary_key

genai.configure(api_key=current_api_key)

class AudioFirstGenerator:
    def __init__(self):
        self.model = genai.GenerativeModel("gemini-1.5-flash")
        self.rag = None
        self.tts = SynchronizedTTS()
        if ManimRAG:
            try:
                self.rag = ManimRAG()
                self.rag.setup()
                print("✅ RAG system initialized")
            except Exception as e:
                print(f"⚠️ RAG init failed: {e}")

    def generate_audio_first_content(self, prompt: str, job_id: str) -> Dict:
        print("🎯 Starting Audio-First Generation")
        script = self._generate_natural_script(prompt)
        audio_path, audio_timing = self._create_natural_audio(script, job_id)
        video_code = self._generate_synchronized_video_code(prompt, script, audio_timing)
        result = {
            "script": script,
            "manim_code": video_code,
            "audio_path": audio_path,
            "audio_timing": audio_timing,
            "natural_duration": audio_timing['total_duration']
        }
        self._save_audio_first_content(result, job_id)
        return result

    def _generate_natural_script(self, prompt: str) -> str:
        relevant_examples = ""
        if self.rag:
            try:
                search_terms = self._extract_search_terms(prompt)
                relevant_examples = self.rag.get_relevant_examples(search_terms)
            except Exception as e:
                print(f"Error getting RAG examples: {e}")

        examples_section = f"RELEVANT EXAMPLES FROM 3BLUE1BROWN:\n{relevant_examples}\n" if relevant_examples else ""

        script_prompt = f"""
You are a 3Blue1Brown-style educational content creator.
Generate a natural, engaging script on: {prompt}

REQUIREMENTS:
- Conversational, educational tone
- Clear, simple language
- Natural pauses & logical flow
- 150-180 words/min
- Suitable for visual animation

STRUCTURE:
- Engaging intro
- Step-by-step concept build
- Use examples & analogies
- Clear conclusion

{examples_section}

TOPIC: {prompt}

Only generate the script text, no code or markers.
"""
        try:
            response = self.model.generate_content(script_prompt)
            return self._clean_script(response.text.strip())
        except Exception as e:
            if "429" in str(e) and "quota" in str(e).lower():
                return self._retry_with_backup_key(script_prompt, "script", prompt)
            return f"Let's explore {prompt} together. We'll visualize it to build intuition."

    def _create_natural_audio(self, script: str, job_id: str) -> Tuple[str, Dict]:
        audio_path = self.tts.generate_timed_audio(script, job_id, None, None)
        timing_file = Path(f"generated/{job_id}/audio_timing.json")
        if timing_file.exists():
            with open(timing_file, 'r') as f:
                audio_timing = json.load(f)
                audio_timing['total_duration'] = audio_timing.get('total_duration_seconds', 0)
        else:
            sentences = extract_sentences(script)
            duration = len(sentences) * 3.0
            audio_timing = {"total_duration": duration, "sentence_count": len(sentences), "segments": []}
        return audio_path, audio_timing

    def _generate_synchronized_video_code(self, prompt: str, script: str, audio_timing: Dict) -> str:
        total_duration = audio_timing.get('total_duration', 30.0)
        segments = audio_timing.get('segments', [])
        examples = self._get_rag_examples(prompt)
        timing_breakdown = self._create_timing_breakdown(segments)

        video_prompt = f"""
You are a professional Manim animation expert using 3Blue1Brown's original manim library. You MUST generate syntactically correct code.

CRITICAL SYNTAX RULES:
- from manimlib import *
- class YourScene(Scene):
- Use axes = Axes(...), graphs = axes.get_graph(...)
- Use ShowCreation(), Write(), Transform(), FadeIn(), FadeOut()
- Use axes.c2p(x, y) for points
- Only Text() for labels
- NEVER Tex(), MathTex(), Create(), GRAY, coords_to_point()
- NEVER animate axes.c2p() directly — create Dot() first
- Transform(old, new) requires two arguments

TIMING INFO:
- Total duration: {total_duration:.2f} seconds
- Segments: {len(segments)}
- Breakdown:
{timing_breakdown}

SCRIPT:
{script}

{examples}

TASK:
Generate code that aligns with the timing of each audio segment. Every segment must trigger a visual change or animation.
Ensure total animation length matches audio duration.
Validate syntax before returning.
"""
        return self._retry_llm_with_validation(video_prompt, prompt, total_duration)

    def _retry_llm_with_validation(self, prompt: str, original_prompt: str, duration: float) -> str:
        for attempt in range(5):
            try:
                response = self.model.generate_content(prompt)
                code = self._clean_manim_code(response.text.strip())
                valid, msg = self._validate_manim_code(code)
                if valid:
                    print(f"✅ Validation passed at attempt {attempt+1}")
                    return code
                prompt += f"\n\n⚠️ Previous attempt failed: {msg}\nPlease correct and regenerate."
            except Exception as e:
                if "429" in str(e) and "quota" in str(e).lower():
                    backup_code = self._retry_with_backup_key(prompt, "video", original_prompt, duration)
                    if backup_code:
                        return backup_code
        raise Exception("❌ Max attempts with invalid code.")