import React, { useState } from 'react';
import './App.css';
import Header from './components/Header';
import VideoGenerator from './components/VideoGenerator';
import JobHistory from './components/JobHistory';
import { Play, History, Info } from 'lucide-react';

function App() {
  const [activeTab, setActiveTab] = useState('generator');
  const [jobs, setJobs] = useState([]);

  const addJob = (job) => {
    setJobs(prev => [job, ...prev]);
  };

  const updateJob = (jobId, updates) => {
    setJobs(prev => prev.map(job => 
      job.id === jobId ? { ...job, ...updates } : job
    ));
  };

  return (
    <div className="App">
      <Header />
      
      <div className="container">
        <nav className="tab-nav">
          <button 
            className={`tab-button ${activeTab === 'generator' ? 'active' : ''}`}
            onClick={() => setActiveTab('generator')}
          >
            <Play size={20} />
            Generate Video
          </button>
          <button 
            className={`tab-button ${activeTab === 'history' ? 'active' : ''}`}
            onClick={() => setActiveTab('history')}
          >
            <History size={20} />
            Job History ({jobs.length})
          </button>
          <button 
            className={`tab-button ${activeTab === 'about' ? 'active' : ''}`}
            onClick={() => setActiveTab('about')}
          >
            <Info size={20} />
            About
          </button>
        </nav>

        <div className="tab-content">
          {activeTab === 'generator' && (
            <VideoGenerator 
              onJobCreate={addJob}
              onJobUpdate={updateJob}
            />
          )}
          
          {activeTab === 'history' && (
            <JobHistory 
              jobs={jobs}
              onJobUpdate={updateJob}
            />
          )}
          
          {activeTab === 'about' && (
            <div className="about-section">
              <div className="card">
                <h2>About 3Blue1Brown Video Generator</h2>
                <p>
                  This application generates educational mathematics videos in the style of 
                  3Blue1Brown using AI-powered script generation and manim animations.
                </p>
                
                <h3>Features</h3>
                <ul>
                  <li>🎬 High-quality MP4 video generation</li>
                  <li>🔊 AI-generated narration with TTS</li>
                  <li>📝 Educational script creation</li>
                  <li>💻 3Blue1Brown manim code generation</li>
                  <li>📊 Real-time progress tracking</li>
                  <li>📁 Easy file downloads</li>
                </ul>

                <h3>How to Use</h3>
                <ol>
                  <li>Enter a mathematical topic (e.g., "derivatives and tangent lines")</li>
                  <li>Select output format (MP4 recommended)</li>
                  <li>Click "Generate Video" and wait for completion</li>
                  <li>Download your generated files</li>
                </ol>

                <div className="tech-info">
                  <h4>Technology Stack</h4>
                  <p>
                    <strong>Backend:</strong> FastAPI, Google Gemini AI, 3Blue1Brown Manim<br/>
                    <strong>Frontend:</strong> React, Lucide Icons<br/>
                    <strong>Audio:</strong> Text-to-Speech with FFmpeg processing
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
