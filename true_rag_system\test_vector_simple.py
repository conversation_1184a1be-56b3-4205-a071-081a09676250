"""
Simple test for vector addition and subtraction without Unicode issues
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_llm import TrueRAGEnhancedManimLLM

def test_vector_addition_simple():
    """Test vector addition with simple output"""
    
    print("🔢 Testing Vector Addition")
    print("=" * 50)
    
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    topic = "vector addition in 2D"
    
    print(f"🎯 Generating: {topic}")
    
    try:
        result = llm.generate_audio_first_video(topic)
        
        if result.get('success'):
            print(f"✅ Generation successful!")
            
            # Show context analysis
            context = result['context_analysis']
            print(f"🧠 Primary Focus: {context.primary_focus}")
            print(f"📊 Complexity: {context.complexity_score:.2f}")
            print(f"🎯 Math Concepts: {len(context.mathematical_concepts)}")
            
            # Save code with UTF-8 encoding
            code_file = Path("vector_addition_simple.py")
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(result['manim_code'])
            print(f"💾 Code saved to: {code_file}")
            
            # Generate video
            print(f"🎬 Generating video...")
            video_path = generate_video_simple(result['manim_code'], "vector_addition")
            
            return result, video_path
        else:
            print(f"❌ Generation failed: {result.get('error')}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_vector_subtraction_simple():
    """Test vector subtraction with simple output"""
    
    print("\n➖ Testing Vector Subtraction")
    print("=" * 50)
    
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    topic = "vector subtraction and difference"
    
    print(f"🎯 Generating: {topic}")
    
    try:
        result = llm.generate_audio_first_video(topic)
        
        if result.get('success'):
            print(f"✅ Generation successful!")
            
            # Show context analysis
            context = result['context_analysis']
            print(f"🧠 Primary Focus: {context.primary_focus}")
            print(f"📊 Complexity: {context.complexity_score:.2f}")
            print(f"🎯 Math Concepts: {len(context.mathematical_concepts)}")
            
            # Save code with UTF-8 encoding
            code_file = Path("vector_subtraction_simple.py")
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(result['manim_code'])
            print(f"💾 Code saved to: {code_file}")
            
            # Generate video
            print(f"🎬 Generating video...")
            video_path = generate_video_simple(result['manim_code'], "vector_subtraction")
            
            return result, video_path
        else:
            print(f"❌ Generation failed: {result.get('error')}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def generate_video_simple(manim_code, job_prefix):
    """Generate video from manim code"""
    
    try:
        from app.manim_3b1b_runner import generate_video_3b1b
        import uuid
        
        job_id = f"{job_prefix}_{uuid.uuid4().hex[:8]}"
        
        video_path = generate_video_3b1b(
            manim_code=manim_code,
            job_id=job_id,
            scene_name="TrueRAGScene",
            output_format="mp4"
        )
        
        if video_path and Path(video_path).exists():
            print(f"✅ Video generated: {video_path}")
            file_size = Path(video_path).stat().st_size / (1024 * 1024)  # MB
            print(f"📊 File size: {file_size:.2f} MB")
            return video_path
        else:
            print(f"❌ Video generation failed")
            return None
            
    except Exception as e:
        print(f"❌ Video generation error: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_operations_simple(add_result, sub_result):
    """Compare the operations"""
    
    print("\n📊 Comparing Vector Operations")
    print("=" * 50)
    
    if not add_result or not sub_result:
        print("❌ Cannot compare - one or both generations failed")
        return
    
    add_code = add_result['manim_code']
    sub_code = sub_result['manim_code']
    
    # Check for operation-specific content
    print("🔍 Operation Analysis:")
    
    # Addition indicators
    add_indicators = [
        'addition' in add_code.lower(),
        'add' in add_code.lower(),
        '+' in add_code,
        'v1_end[0] + v2_end[0]' in add_code
    ]
    
    # Subtraction indicators  
    sub_indicators = [
        'subtraction' in sub_code.lower(),
        'subtract' in sub_code.lower(),
        '-' in sub_code,
        'v1_end[0] - v2_end[0]' in sub_code,
        'negative' in sub_code.lower()
    ]
    
    print(f"📈 Addition indicators: {sum(add_indicators)}/4")
    print(f"📉 Subtraction indicators: {sum(sub_indicators)}/5")
    
    # Check for different mathematical operations
    if 'v1_end[0] + v2_end[0]' in add_code:
        print("✅ Addition: Found vector component addition")
    if 'v1_end[0] - v2_end[0]' in sub_code:
        print("✅ Subtraction: Found vector component subtraction")
    if 'negative' in sub_code.lower() or '-v2' in sub_code:
        print("✅ Subtraction: Found negative vector concept")
    
    # Calculate similarity
    add_lines = set(line.strip() for line in add_code.split('\n') if line.strip())
    sub_lines = set(line.strip() for line in sub_code.split('\n') if line.strip())
    
    intersection = len(add_lines.intersection(sub_lines))
    union = len(add_lines.union(sub_lines))
    similarity = (intersection / union) * 100 if union > 0 else 0
    
    print(f"📊 Code similarity: {similarity:.1f}%")
    
    if similarity < 70:
        print("✅ SUCCESS: Addition and subtraction generate different code!")
    else:
        print("⚠️ WARNING: Addition and subtraction are too similar")
    
    return similarity

def show_code_preview(code, title):
    """Show a preview of the generated code"""
    
    print(f"\n📝 {title} Code Preview:")
    print("-" * 40)
    
    lines = code.split('\n')
    for i, line in enumerate(lines[:20], 1):  # Show first 20 lines
        print(f"{i:2d}: {line}")
    
    if len(lines) > 20:
        print(f"... and {len(lines) - 20} more lines")

def main():
    """Main test function"""
    
    print("🚀 Testing True RAG Vector Operations (Simple)")
    print("=" * 70)
    print("Testing vector addition and subtraction with proper encoding")
    print("=" * 70)
    
    # Test 1: Vector Addition
    add_result, add_video = test_vector_addition_simple()
    
    # Test 2: Vector Subtraction  
    sub_result, sub_video = test_vector_subtraction_simple()
    
    # Test 3: Compare operations
    if add_result and sub_result:
        similarity = compare_operations_simple(add_result, sub_result)
        
        # Show code previews
        show_code_preview(add_result['manim_code'], "Vector Addition")
        show_code_preview(sub_result['manim_code'], "Vector Subtraction")
    
    # Summary
    print(f"\n🎉 Vector Operations Test Summary")
    print("=" * 50)
    
    if add_video:
        print(f"✅ Vector Addition Video: {add_video}")
    else:
        print(f"❌ Vector Addition: Failed")
        
    if sub_video:
        print(f"✅ Vector Subtraction Video: {sub_video}")
    else:
        print(f"❌ Vector Subtraction: Failed")
    
    if add_result and sub_result:
        print(f"📊 Code Similarity: {similarity:.1f}%")
        
        if similarity < 70:
            print(f"🎉 SUCCESS: Different operations generate different code!")
        else:
            print(f"⚠️ Operations are too similar")
    
    if add_video and sub_video:
        print(f"\n🎉 COMPLETE SUCCESS!")
        print(f"✅ Vector addition and subtraction both working")
        print(f"✅ Topic-specific content generation confirmed")
        print(f"✅ Real video output for both operations")
        print(f"✅ True RAG system handles different vector operations")

if __name__ == "__main__":
    main()
