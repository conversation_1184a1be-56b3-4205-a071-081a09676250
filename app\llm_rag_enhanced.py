"""
RAG-Enhanced LLM for 3Blue1Brown Manim Code Generation
This module integrates the RAG system with the LLM to provide better manim code generation
"""

import os
import json
import google.generativeai as genai
from pathlib import Path
from dotenv import load_dotenv
from typing import Optional

try:
    from app.rag_system import ManimRAG
except ImportError:
    print("RAG system not available. Run setup_3b1b_rag.py first.")
    ManimRAG = None

load_dotenv()
genai.configure(api_key=os.getenv("GOOGLE_API_KEY2"))
model = genai.GenerativeModel("gemini-1.5-flash")


class RAGEnhancedManimLLM:
    """LLM enhanced with RAG for better manim code generation"""
    
    def __init__(self):
        self.rag = None
        if ManimRAG:
            try:
                self.rag = ManimRAG()
                self.rag.setup()
                print("✅ RAG system initialized")
            except Exception as e:
                print(f"⚠️ RAG system failed to initialize: {e}")
                self.rag = None
        else:
            print("⚠️ RAG system not available")
    
    def get_relevant_examples(self, prompt: str) -> str:
        """Get relevant manim examples from RAG system"""
        if not self.rag:
            return ""
        
        try:
            # Extract key concepts from prompt for better search
            search_terms = self._extract_search_terms(prompt)
            examples = self.rag.get_relevant_examples(search_terms)
            return examples
        except Exception as e:
            print(f"Error getting examples: {e}")
            return ""
    
    def _extract_search_terms(self, prompt: str) -> str:
        """Extract key terms from prompt for RAG search"""
        # Simple keyword extraction - could be enhanced with NLP
        math_terms = [
            "vector", "matrix", "derivative", "integral", "function",
            "graph", "plot", "equation", "formula", "geometry",
            "calculus", "algebra", "trigonometry", "statistics",
            "probability", "complex", "number", "transformation"
        ]

        animation_terms = [
            "animation", "transform", "create", "write", "draw",
            "move", "rotate", "scale", "fade", "morph", "shift"
        ]

        found_terms = []
        prompt_lower = prompt.lower()

        for term in math_terms + animation_terms:
            if term in prompt_lower:
                found_terms.append(term)

        return " ".join(found_terms) if found_terms else prompt

    def _validate_manim_code(self, code: str) -> tuple[bool, str]:
        """Validate manim code for common syntax errors"""
        errors = []

        # Check basic syntax
        try:
            compile(code, '<string>', 'exec')
        except SyntaxError as e:
            errors.append(f"Syntax error: {e}")

        # Check for required imports
        if "from manimlib import *" not in code:
            errors.append("Missing required import: from manimlib import *")

        # Check for Scene class
        if "class " not in code or "(Scene)" not in code:
            errors.append("Missing Scene class definition")

        # Check for construct method
        if "def construct(self):" not in code:
            errors.append("Missing construct method")

        # Check for common 3b1b patterns
        if "FunctionGraph(" in code and "axes.get_graph(" not in code:
            errors.append("Use axes.get_graph() instead of FunctionGraph() for 3b1b manim")

        # Check for balanced parentheses
        if code.count('(') != code.count(')'):
            errors.append("Unbalanced parentheses")

        if code.count('[') != code.count(']'):
            errors.append("Unbalanced square brackets")

        if code.count('{') != code.count('}'):
            errors.append("Unbalanced curly braces")

        # Check for common variable misuse patterns
        lines = code.split('\n')
        variables = {}  # Track variable assignments

        for i, line in enumerate(lines, 1):
            line = line.strip()

            # Track variable assignments
            if '=' in line and not line.startswith('#'):
                parts = line.split('=', 1)
                if len(parts) == 2:
                    var_name = parts[0].strip()
                    assignment = parts[1].strip()

                    # Track what type of object this variable holds
                    if 'axes.i2gp(' in assignment:
                        variables[var_name] = 'point_coordinates'
                    elif 'Dot(' in assignment:
                        variables[var_name] = 'dot_object'
                    elif 'Text(' in assignment:
                        variables[var_name] = 'text_object'

            # Check for misuse in animations
            if 'FadeOut(' in line or 'FadeIn(' in line:
                # Extract variable names in FadeOut/FadeIn
                import re
                fade_vars = re.findall(r'Fade(?:Out|In)\(([^,)]+)', line)
                for var in fade_vars:
                    var = var.strip()
                    if var in variables and variables[var] == 'point_coordinates':
                        errors.append(f"Line {i}: Cannot use FadeOut/FadeIn on point coordinates. Use Dot(point) first.")

        return len(errors) == 0, "; ".join(errors)
    
    def generate_script_and_manim_code(self, prompt: str, job_id: str) -> dict:
        """Generate script and manim code with RAG enhancement"""

        # Get relevant examples from RAG
        relevant_examples = self.get_relevant_examples(prompt)

        # Enhanced prompt with examples and strict syntax rules
        examples_section = ""
        if relevant_examples:
            examples_section = f"RELEVANT EXAMPLES FROM 3BLUE1BROWN CODEBASE:\n{relevant_examples}\n"

        user_prompt = f"""
You are a professional Manim animation expert using 3Blue1Brown's original manim library. You MUST generate syntactically correct code.

CRITICAL SYNTAX RULES FOR 3BLUE1BROWN MANIM:

1. IMPORTS AND SCENE:
   - ALWAYS use: from manimlib import *
   - Scene class: class YourScene(Scene):
   - NO CONFIG dictionary needed

2. AXES AND GRAPHS:
   - Use: axes = Axes(x_range=(-3, 3, 1), y_range=(-2, 8, 1))
   - For graphs: graph = axes.get_graph(lambda x: x**2, color=BLUE)
   - NOT FunctionGraph directly
   - Use axes.i2gp(x_value, graph) for points on graph

3. COMMON OBJECTS:
   - Text: Text("Hello", font_size=36)
   - Dot: Dot(color=RED)
   - Line: Line(start=LEFT, end=RIGHT)
   - Circle: Circle(radius=1, color=BLUE)

4. ANIMATIONS:
   - ShowCreation (not Create)
   - Write (for text)
   - FadeIn, FadeOut
   - Transform (not ReplacementTransform)

5. POSITIONING:
   - Use .to_edge(UP), .next_to(obj, DOWN)
   - Use .shift(UP * 2) for relative movement
   - Use .move_to(point) for absolute positioning

6. UPDATERS:
   - obj.add_updater(lambda m: m.move_to(...))
   - obj.clear_updaters() when done

7. VALUE TRACKERS:
   - tracker = ValueTracker(0)
   - tracker.get_value()
   - tracker.animate.set_value(5)

8. CRITICAL MISTAKES TO AVOID:
   - DON'T use point coordinates directly in animations
   - axes.i2gp() returns coordinates, wrap in Dot() for animations
   - Example: point_coords = axes.i2gp(2, graph); dot = Dot(point_coords)
   - Then use: self.play(FadeIn(dot)) NOT self.play(FadeIn(point_coords))

{examples_section}

TASK: Generate educational content for: {prompt}

REQUIREMENTS:
- Code MUST be syntactically correct for 3Blue1Brown's manim
- Use ONLY the syntax patterns shown above
- Test each line mentally for correctness
- Include proper wait() calls between animations
- Make animations educational and smooth
- NEVER animate point coordinates directly - always wrap in Dot()

OUTPUT FORMAT (JSON):
{{
  "script": "Educational narration script...",
  "manim_code": "Syntactically correct 3b1b manim code..."
}}

VALIDATION CHECKLIST:
✓ Uses 'from manimlib import *'
✓ Scene class inherits from Scene
✓ Uses Axes() and axes.get_graph() for functions
✓ Uses correct animation names (ShowCreation, Write, etc.)
✓ All parentheses and brackets are balanced
✓ No undefined variables or methods
✓ Proper indentation
✓ Point coordinates wrapped in Dot() before animating

Return ONLY valid JSON without markdown formatting."""

        # Try generation with validation and retry logic
        max_attempts = 3
        data = None

        for attempt in range(max_attempts):
            try:
                response = model.generate_content(user_prompt)

                # Handle response
                output = response.text.strip()

                # Remove markdown code block markers
                if output.startswith("```json"):
                    output = output[7:]
                if output.startswith("```"):
                    output = output[3:]
                if output.endswith("```"):
                    output = output[:-3]

                output = output.strip()

                # Try to parse JSON
                try:
                    data = json.loads(output)
                except json.JSONDecodeError as e:
                    if attempt < max_attempts - 1:
                        print(f"⚠️ JSON parsing failed on attempt {attempt + 1}: {e}")
                        user_prompt += f"\n\nPREVIOUS ATTEMPT HAD INVALID JSON: {e}\nReturn ONLY valid JSON without any extra text or formatting."
                        continue
                    else:
                        # Try to clean up the response as fallback
                        import re
                        start = output.find('{')
                        end = output.rfind('}') + 1
                        if start >= 0 and end > start:
                            json_part = output[start:end]
                            cleaned = re.sub(r'"\s*\n\s*', '" ', json_part)
                            cleaned = re.sub(r'\n\s*"', ' "', cleaned)
                            data = json.loads(cleaned)
                        else:
                            raise e

                # Validate the generated manim code
                is_valid, error_msg = self._validate_manim_code(data["manim_code"])

                if is_valid:
                    print(f"✅ Code validation passed on attempt {attempt + 1}")
                    break
                else:
                    print(f"⚠️ Code validation failed on attempt {attempt + 1}: {error_msg}")
                    if attempt < max_attempts - 1:
                        # Add validation feedback to prompt for retry
                        user_prompt += f"\n\nPREVIOUS ATTEMPT FAILED WITH ERRORS: {error_msg}\nFIX THESE ERRORS IN YOUR NEXT RESPONSE."
                        continue
                    else:
                        print("❌ Max attempts reached, proceeding with last attempt")
                        break

            except Exception as e:
                if attempt < max_attempts - 1:
                    print(f"⚠️ Generation failed on attempt {attempt + 1}: {e}")
                    continue
                else:
                    raise e

        if data is None:
            raise ValueError("Failed to generate valid content after all attempts")

        # Save output
        base_path = Path(f"generated/{job_id}")
        base_path.mkdir(parents=True, exist_ok=True)

        with open(base_path / "script.txt", "w", encoding="utf-8") as f:
            f.write(data["script"])

        with open(base_path / "manim_code.py", "w", encoding="utf-8") as f:
            f.write(data["manim_code"])
        
        # Save RAG examples used (for debugging)
        if relevant_examples:
            with open(base_path / "rag_examples.txt", "w", encoding="utf-8") as f:
                f.write("RAG Examples Used:\n")
                f.write("=" * 50 + "\n")
                f.write(relevant_examples)

        return {
            "script": data["script"],
            "manim_code": data["manim_code"],
            "job_id": job_id,
            "rag_enhanced": bool(relevant_examples)
        }


# Backward compatibility function
def generate_script_and_manim_code(prompt: str, job_id: str) -> dict:
    """Backward compatible function for existing code"""
    llm = RAGEnhancedManimLLM()
    return llm.generate_script_and_manim_code(prompt, job_id)


def main():
    """Test the RAG-enhanced LLM"""
    llm = RAGEnhancedManimLLM()
    
    # Test with a simple prompt
    test_prompt = "vector addition visualization"
    test_job_id = "rag_test"
    
    print(f"Testing with prompt: {test_prompt}")
    
    try:
        result = llm.generate_script_and_manim_code(test_prompt, test_job_id)
        print("✅ Generation successful!")
        print(f"RAG Enhanced: {result.get('rag_enhanced', False)}")
        print(f"Script length: {len(result['script'])} characters")
        print(f"Code length: {len(result['manim_code'])} characters")
    except Exception as e:
        print(f"❌ Generation failed: {e}")


if __name__ == "__main__":
    main()
