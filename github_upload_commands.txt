# GitHub Upload Commands
# Run these commands after creating your GitHub repository

# 1. Add your GitHub repository as remote origin
# Replace 'yourusername' with your actual GitHub username
git remote add origin https://github.com/yourusername/rag-3blue1brown-generator.git

# 2. Push to GitHub
git push -u origin main

# Alternative: If you get authentication errors, you might need to use a personal access token
# Go to GitHub Settings > Developer settings > Personal access tokens > Generate new token
# Then use:
# git remote set-url origin https://yourusername:<EMAIL>/yourusername/rag-3blue1brown-generator.git
# git push -u origin main

# 3. Verify upload
# Check your GitHub repository page to confirm all files are uploaded

# 4. Future updates
# After making changes, use:
# git add .
# git commit -m "Your commit message"
# git push
