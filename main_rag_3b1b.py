"""
Main script using RAG-enhanced LLM with 3Blue1Brown's manim
This script demonstrates the complete pipeline with RAG and 3b1b manim
"""

from dotenv import load_dotenv
from app.llm_rag_enhanced import generate_script_and_manim_code
from app.manim_3b1b_runner import generate_video_3b1b, check_3b1b_manim_installation
from app.tts import text_to_speech

# Load environment variables
load_dotenv()

def main(custom_prompt=None, output_format="mp4"):
    """Main function demonstrating RAG-enhanced 3b1b manim pipeline"""

    print("🚀 RAG-Enhanced 3Blue1Brown Manim Pipeline")
    print("=" * 60)

    # Configuration
    if custom_prompt:
        prompt = custom_prompt
        job_id = "custom_topic"
    else:
        prompt = input("Enter your topic (or press Enter for default): ").strip()
        if not prompt:
            prompt = "visualizing the derivative as the slope of a tangent line"
        job_id = "rag_3b1b_test"
    
    print(f"📝 Topic: {prompt}")
    print(f"🆔 Job ID: {job_id}")
    print(f"🎬 Output Format: {output_format.upper()}")

    # Step 1: Check 3b1b manim installation
    print("\n🔍 Checking 3Blue1Brown manim installation...")
    if not check_3b1b_manim_installation():
        print("❌ 3b1b manim not found!")
        print("Please run: python setup_3b1b_rag.py")
        return
    print("✅ 3b1b manim is available")
    
    # Step 2: Generate script and manim code with RAG
    print("\n🤖 Generating script and manim code with RAG enhancement...")
    try:
        result = generate_script_and_manim_code(prompt, job_id)
        print("✅ Generation successful!")
        print(f"📊 RAG Enhanced: {result.get('rag_enhanced', False)}")
        print(f"📄 Script saved to: generated/{job_id}/script.txt")
        print(f"🐍 Manim code saved to: generated/{job_id}/manim_code.py")
        
        if result.get('rag_enhanced'):
            print(f"📚 RAG examples saved to: generated/{job_id}/rag_examples.txt")
        
    except Exception as e:
        print(f"❌ Generation failed: {e}")
        return
    
    # Step 3: Generate video with 3b1b manim
    print(f"\n🎬 Generating {output_format.upper()} with 3Blue1Brown manim...")
    try:
        video_path = generate_video_3b1b(result['manim_code'], job_id, output_format=output_format)
        print(f"✅ {output_format.upper()} generated: {video_path}")
    except Exception as e:
        print(f"❌ Video generation failed: {e}")
        print("The script and code were generated successfully, but video rendering failed.")
        print("You can try running the manim code manually.")
        # Continue with TTS even if video fails
    
    # Step 4: Generate TTS audio
    print("\n🎵 Generating text-to-speech audio...")
    try:
        audio_path = text_to_speech(result['script'], job_id)
        if audio_path:
            print(f"✅ Audio generated: {audio_path}")
        else:
            print("❌ Audio generation failed")
    except Exception as e:
        print(f"❌ Audio generation failed: {e}")
    
    # Summary
    print("\n📋 Pipeline Summary:")
    print("=" * 30)
    print(f"Topic: {prompt}")
    print(f"Job ID: {job_id}")
    print(f"RAG Enhanced: {result.get('rag_enhanced', 'Unknown')}")
    print(f"Files generated in: generated/{job_id}/")
    
    print("\n🎯 Next Steps:")
    print("1. Review the generated script and manim code")
    print("2. If video generation failed, debug the manim code")
    print("3. Combine video and audio for final output")
    print("4. Iterate and improve based on results")


def test_rag_system():
    """Test the RAG system independently"""
    print("🧪 Testing RAG System")
    print("=" * 30)
    
    try:
        from app.rag_system import ManimRAG
        
        rag = ManimRAG()
        rag.setup()
        
        # Test search
        test_queries = [
            "vector animation",
            "derivative visualization",
            "matrix transformation",
            "graph plotting"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Searching for: {query}")
            results = rag.search(query, n_results=2)
            
            for i, result in enumerate(results):
                print(f"  Result {i+1}:")
                print(f"    Type: {result['metadata'].get('content_type')}")
                print(f"    File: {result['metadata'].get('relative_path')}")
                print(f"    Preview: {result['content'][:100]}...")
        
        print("\n✅ RAG system test completed")
        
    except Exception as e:
        print(f"❌ RAG system test failed: {e}")


def setup_check():
    """Check if all components are properly set up"""
    print("🔧 Setup Check")
    print("=" * 20)
    
    checks = []
    
    # Check 3b1b repositories
    from pathlib import Path
    manim_repo = Path("3b1b_data/manim")
    videos_repo = Path("3b1b_data/videos")
    
    checks.append(("3b1b manim repo", manim_repo.exists()))
    checks.append(("3b1b videos repo", videos_repo.exists()))
    
    # Check 3b1b manim installation
    checks.append(("3b1b manim installed", check_3b1b_manim_installation()))
    
    # Check RAG system
    try:
        from app.rag_system import ManimRAG
        rag = ManimRAG()
        rag_available = True
    except:
        rag_available = False
    
    checks.append(("RAG system available", rag_available))
    
    # Check environment variables
    import os
    checks.append(("GOOGLE_API_KEY set", bool(os.getenv("GOOGLE_API_KEY"))))
    
    # Print results
    all_good = True
    for check_name, status in checks:
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {check_name}")
        if not status:
            all_good = False
    
    if all_good:
        print("\n🎉 All systems ready!")
    else:
        print("\n⚠️ Some components need setup. Run: python setup_3b1b_rag.py")
    
    return all_good


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "test-rag":
            test_rag_system()
        elif sys.argv[1] == "check":
            setup_check()
        elif sys.argv[1].startswith('"') or not sys.argv[1].startswith('-'):
            # Custom topic provided
            custom_topic = " ".join(sys.argv[1:])
            output_format = "mp4"  # Default format

            # Check for format specification
            if "--gif" in custom_topic:
                output_format = "gif"
                custom_topic = custom_topic.replace("--gif", "").strip()
            elif "--mp4" in custom_topic:
                output_format = "mp4"
                custom_topic = custom_topic.replace("--mp4", "").strip()

            # Remove quotes if present
            if custom_topic.startswith('"') and custom_topic.endswith('"'):
                custom_topic = custom_topic[1:-1]
            main(custom_topic, output_format)
        else:
            print("Usage: python main_rag_3b1b.py [topic] [--mp4|--gif] [test-rag|check]")
            print('Examples:')
            print('  python main_rag_3b1b.py "quadratic functions"')
            print('  python main_rag_3b1b.py "circle animation" --gif')
            print('  python main_rag_3b1b.py "derivatives" --mp4')
    else:
        main()
