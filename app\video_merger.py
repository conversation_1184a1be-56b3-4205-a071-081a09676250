import subprocess
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


def find_ffmpeg():
    """Find FFmpeg executable"""
    # Common FFmpeg locations
    possible_paths = [
        "ffmpeg",  # In PATH
        "C:/ffmpeg/bin/ffmpeg.exe",
        "C:/Program Files/ffmpeg/bin/ffmpeg.exe",
        "/usr/bin/ffmpeg",
        "/usr/local/bin/ffmpeg"
    ]

    for path in possible_paths:
        try:
            subprocess.run([path, "-version"], capture_output=True, check=True)
            return path
        except (subprocess.CalledProcessError, FileNotFoundError):
            continue

    raise FileNotFoundError("FFmpeg not found. Please install FFmpeg.")

def merge_audio_video(audio_path: str, video_path: str, job_id: str,
                     output_name: str = "final_video") -> str:
    """
    Merge audio and video files using FFmpeg

    Args:
        audio_path: Path to audio file (MP3)
        video_path: Path to video file (MP4)
        job_id: Job identifier for output directory
        output_name: Name for output file (without extension)

    Returns:
        Path to merged video file
    """
    try:
        # Find FFmpeg
        ffmpeg_path = find_ffmpeg()

        # Setup paths
        base_path = Path(f"generated/{job_id}")
        base_path.mkdir(parents=True, exist_ok=True)
        final_path = base_path / f"{output_name}.mp4"

        # Remove existing file if it exists
        if final_path.exists():
            final_path.unlink()

        # Verify input files exist
        if not Path(audio_path).exists():
            raise FileNotFoundError(f"Audio file not found: {audio_path}")
        if not Path(video_path).exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")

        logger.info(f"Merging video: {video_path} with audio: {audio_path}")

        # Get video and audio durations first
        video_info = get_video_info(video_path)
        audio_info = get_video_info(audio_path)

        # Extract durations
        video_duration = get_duration_from_info(video_info)
        audio_duration = get_duration_from_info(audio_info)

        logger.info(f"Video duration: {video_duration}s, Audio duration: {audio_duration}s")

        # Use simple merge strategy - always use shortest duration
        # This is more reliable and avoids complex filter issues
        command = [
            ffmpeg_path,
            "-i", str(video_path),
            "-i", str(audio_path),
            "-c:v", "copy",  # Copy video stream without re-encoding
            "-c:a", "aac",   # Re-encode audio to AAC
            "-map", "0:v:0",  # Map video from first input
            "-map", "1:a:0",  # Map audio from second input
            "-shortest",      # Use shortest duration (prevents blank sections)
            "-avoid_negative_ts", "make_zero",  # Fix timing issues
            "-y",             # Overwrite output file
            str(final_path)
        ]

        logger.info("Using simple merge strategy with shortest duration")
        expected_duration = min(video_duration, audio_duration)
        logger.info(f"Expected output duration: {expected_duration:.2f}s")

        # Log the command for debugging
        logger.info(f"FFmpeg command: {' '.join(command)}")

        # Run FFmpeg
        result = subprocess.run(
            command,
            capture_output=True,
            text=True,
            check=True
        )

        # Log FFmpeg output for debugging
        if result.stderr:
            logger.info(f"FFmpeg stderr: {result.stderr}")
        if result.stdout:
            logger.info(f"FFmpeg stdout: {result.stdout}")

        logger.info(f"Video merge completed: {final_path}")
        return str(final_path)

    except subprocess.CalledProcessError as e:
        error_msg = f"FFmpeg error: {e.stderr}"
        logger.error(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        logger.error(f"Video merge failed: {e}")
        raise

def get_duration_from_info(media_info: dict) -> float:
    """Extract duration from media info"""
    try:
        if 'format' in media_info and 'duration' in media_info['format']:
            return float(media_info['format']['duration'])

        # Fallback: check streams
        if 'streams' in media_info:
            for stream in media_info['streams']:
                if 'duration' in stream:
                    return float(stream['duration'])

        logger.warning("Could not extract duration from media info")
        return 10.0  # Default fallback duration
    except Exception as e:
        logger.warning(f"Error extracting duration: {e}")
        return 10.0


def get_video_info(video_path: str) -> dict:
    """Get video information using FFprobe"""
    try:
        ffmpeg_path = find_ffmpeg()
        ffprobe_path = ffmpeg_path.replace("ffmpeg", "ffprobe")

        command = [
            ffprobe_path,
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams",
            str(video_path)
        ]

        result = subprocess.run(command, capture_output=True, text=True, check=True)

        import json
        return json.loads(result.stdout)

    except Exception as e:
        logger.warning(f"Could not get video info: {e}")
        return {}
