"""
Manim runner for 3Blue1Brown's original manim library
This module handles running animations with 3b1b's manim instead of ManimCommunity
"""

import subprocess
import os
from pathlib import Path
import sys


def generate_video_3b1b(manim_code: str, job_id: str, scene_name: str = None, output_format: str = "mp4") -> str:
    """
    Generate video using 3Blue1Brown's original manim library

    Args:
        manim_code: The manim code to execute
        job_id: Unique identifier for this job
        scene_name: Name of the scene class (auto-detected if None)
        output_format: Output format - "mp4" (default) or "gif"

    Returns:
        Path to the generated video file
    """
    
    # Create output directory
    output_dir = Path(f"generated/{job_id}")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Save the manim code
    code_file = output_dir / "manim_code.py"
    with open(code_file, "w", encoding="utf-8") as f:
        f.write(manim_code)
    
    # Auto-detect scene name if not provided
    if scene_name is None:
        scene_name = extract_scene_name(manim_code)
    
    if not scene_name:
        raise ValueError("Could not detect scene name in manim code")
    
    # 3b1b manim command structure
    # python -m manimlib scene_file.py SceneName
    command = [
        sys.executable, "-m", "manimlib",
        str(code_file),
        scene_name,
        "--hd",            # High quality rendering (1080p)
        "-w",              # Write to file
        "--video_dir", str(output_dir)
    ]

    # Add format-specific flags
    if output_format.lower() == "gif":
        command.append("-i")  # Save as GIF
    # For MP4, no additional flag needed (default)
    
    print(f"Running 3b1b manim command: {' '.join(command)}")
    
    try:
        # Change to the code directory for relative imports
        original_cwd = os.getcwd()
        os.chdir(output_dir.parent.parent)  # Go to project root
        
        # Run the manim command
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
            cwd=str(output_dir.parent.parent)
        )
        
        print("3b1b Manim output:")
        print(result.stdout)
        if result.stderr:
            print("Warnings/Errors:")
            print(result.stderr)
        
        # Find the generated video file
        video_file = find_generated_video(output_dir, scene_name, output_format)
        
        if video_file:
            print(f"✅ Video generated: {video_file}")
            return str(video_file)
        else:
            raise FileNotFoundError("Generated video file not found")
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Manim execution failed:")
        print(f"Return code: {e.returncode}")
        print(f"stdout: {e.stdout}")
        print(f"stderr: {e.stderr}")
        raise
    finally:
        os.chdir(original_cwd)


def extract_scene_name(manim_code: str) -> str:
    """
    Extract the scene class name from manim code
    
    Args:
        manim_code: The manim code string
        
    Returns:
        The name of the scene class
    """
    import re
    
    # Look for class definitions that inherit from Scene
    pattern = r'class\s+(\w+)\s*\([^)]*Scene[^)]*\):'
    matches = re.findall(pattern, manim_code)
    
    if matches:
        return matches[0]  # Return the first scene class found
    
    # Fallback: look for any class definition
    pattern = r'class\s+(\w+)\s*\([^)]*\):'
    matches = re.findall(pattern, manim_code)
    
    if matches:
        return matches[0]
    
    return None


def find_generated_video(output_dir: Path, scene_name: str, output_format: str = "mp4") -> Path:
    """
    Find the generated video file in the output directory

    Args:
        output_dir: Directory where files are generated
        scene_name: Name of the scene class
        output_format: Expected output format ("mp4" or "gif")

    Returns:
        Path to the video file
    """

    # 3b1b manim typically generates files in subdirectories
    # Prioritize the requested format, then fallback to others
    if output_format.lower() == "gif":
        video_extensions = ['.gif', '.mp4', '.mov', '.avi']
    else:
        video_extensions = ['.mp4', '.mov', '.avi', '.gif']
    
    # Search in the output directory and subdirectories
    for ext in video_extensions:
        # Direct file
        video_file = output_dir / f"{scene_name}{ext}"
        if video_file.exists():
            return video_file
        
        # In subdirectories (common with 3b1b manim)
        for subdir in output_dir.iterdir():
            if subdir.is_dir():
                video_file = subdir / f"{scene_name}{ext}"
                if video_file.exists():
                    return video_file
                
                # Sometimes files are in deeper subdirectories
                for subsubdir in subdir.iterdir():
                    if subsubdir.is_dir():
                        video_file = subsubdir / f"{scene_name}{ext}"
                        if video_file.exists():
                            return video_file
    
    # If not found with scene name, look for any video file
    for ext in video_extensions:
        for video_file in output_dir.rglob(f"*{ext}"):
            return video_file
    
    return None


def check_3b1b_manim_installation() -> bool:
    """
    Check if 3Blue1Brown's manim is properly installed
    
    Returns:
        True if 3b1b manim is available, False otherwise
    """
    try:
        result = subprocess.run(
            [sys.executable, "-m", "manimlib", "--help"],
            capture_output=True,
            text=True,
            timeout=10
        )
        return result.returncode == 0
    except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
        return False


def install_3b1b_manim():
    """
    Install 3Blue1Brown's manim from the cloned repository
    """
    manim_path = Path("3b1b_data/manim")
    
    if not manim_path.exists():
        raise FileNotFoundError(
            "3b1b manim repository not found. "
            "Run setup_3b1b_rag.py first to clone the repository."
        )
    
    print("Installing 3Blue1Brown's manim...")
    
    try:
        # Install in development mode
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-e", str(manim_path)
        ])
        print("✅ 3b1b manim installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install 3b1b manim: {e}")
        return False


def main():
    """Test the 3b1b manim runner"""
    
    # Check if 3b1b manim is installed
    if not check_3b1b_manim_installation():
        print("3b1b manim not found. Attempting to install...")
        if not install_3b1b_manim():
            print("Failed to install 3b1b manim. Please run setup_3b1b_rag.py first.")
            return
    
    # Test with a simple scene
    test_code = '''
from manimlib import *

class TestScene(Scene):
    def construct(self):
        text = Text("Hello from 3Blue1Brown Manim!")
        self.play(Write(text))
        self.wait(2)
        
        circle = Circle(radius=2, color=BLUE)
        self.play(Transform(text, circle))
        self.wait(2)
'''
    
    try:
        video_path = generate_video_3b1b(test_code, "3b1b_test")
        print(f"✅ Test successful! Video generated at: {video_path}")
    except Exception as e:
        print(f"❌ Test failed: {e}")


if __name__ == "__main__":
    main()
