"""
Test the fixed derivative code generation
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_llm import TrueRAGEnhancedManimLLM

def test_fixed_derivative():
    """Test the fixed derivative code generation"""
    
    print("🧮 Testing Fixed Derivative Code Generation")
    print("=" * 60)
    
    # Initialize the True RAG Enhanced LLM
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    
    # Test derivative topic
    topic = "derivatives and tangent lines"
    
    print(f"🎯 Generating code for: {topic}")
    
    try:
        # Generate using True RAG
        result = llm.generate_audio_first_video(topic)
        
        if result.get('success'):
            print(f"✅ Code generation successful!")
            
            # Save the code
            code_file = Path("fixed_derivative_code.py")
            with open(code_file, 'w') as f:
                f.write(result['manim_code'])
            
            print(f"💾 Code saved to: {code_file}")
            
            # Show code preview
            print(f"\n📝 Generated Code Preview:")
            print("-" * 40)
            lines = result['manim_code'].split('\n')
            for i, line in enumerate(lines[:30], 1):  # Show first 30 lines
                print(f"{i:2d}: {line}")
            if len(lines) > 30:
                print(f"... and {len(lines) - 30} more lines")
            
            # Try to generate video
            print(f"\n🎬 Attempting video generation...")
            try:
                from app.manim_3b1b_runner import generate_video_3b1b
                import uuid
                
                job_id = f"fixed_derivative_{uuid.uuid4().hex[:8]}"
                
                video_path = generate_video_3b1b(
                    manim_code=result['manim_code'],
                    job_id=job_id,
                    scene_name="TrueRAGScene",
                    output_format="mp4"
                )
                
                if video_path and Path(video_path).exists():
                    print(f"✅ Video generated successfully!")
                    print(f"📹 Video file: {video_path}")
                    
                    # Get file size
                    file_size = Path(video_path).stat().st_size / (1024 * 1024)  # MB
                    print(f"📊 File size: {file_size:.2f} MB")
                    
                    return video_path
                else:
                    print(f"❌ Video generation failed - file not found")
                    
            except Exception as e:
                print(f"❌ Video generation error: {e}")
                import traceback
                traceback.print_exc()
        
        else:
            print(f"❌ Code generation failed: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
    
    return None

def main():
    """Main test function"""
    
    print("🚀 Testing Fixed True RAG Derivative Generation")
    print("=" * 70)
    
    video_path = test_fixed_derivative()
    
    if video_path:
        print(f"\n🎉 SUCCESS!")
        print(f"✅ True RAG system working perfectly")
        print(f"✅ Topic-specific code generation: Working")
        print(f"✅ Video generation: Working")
        print(f"📹 Generated video: {video_path}")
        print(f"\n🎯 The True RAG system now generates:")
        print(f"   - Different code for different topics")
        print(f"   - Appropriate mathematical animations")
        print(f"   - Error-free manim code")
        print(f"   - Successful video rendering")
    else:
        print(f"\n⚠️ Still some issues to resolve")

if __name__ == "__main__":
    main()
