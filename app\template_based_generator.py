#!/usr/bin/env python3
"""
Template-Based Manim Code Generator
Forces LLM to fill in safe templates instead of generating code from scratch
"""

import os
import json
import google.generativeai as genai
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, List

load_dotenv()
genai.configure(api_key=os.getenv("GOOGLE_API_KEY"))
model = genai.GenerativeModel("gemini-1.5-flash")


class TemplateManimGenerator:
    """Generate manim code using safe templates"""
    
    def __init__(self):
        self.safe_templates = self._create_safe_templates()
    
    def _create_safe_templates(self) -> Dict:
        """Create safe, validated templates"""
        return {
            "basic_scene": """from manimlib import *

class {scene_name}(Scene):
    def construct(self):
        # Setup coordinate system
        axes = Axes(x_range=(-5, 5, 1), y_range=(-5, 5, 1))
        self.play(ShowCreation(axes), run_time=2)
        
{segments}
""",
            
            "text_segment": """        # Segment {segment_num}: {description}
        text{segment_num} = Text("{text_content}").to_edge(UP)
        self.play(Write(text{segment_num}), run_time={text_duration})
        self.play(FadeOut(text{segment_num}), run_time=0.5)
""",
            
            "graph_segment": """        # Mathematical visualization: {description}
        graph{segment_num} = axes.get_graph(lambda x: {function}, color={color})
        self.play(ShowCreation(graph{segment_num}), run_time={duration})
""",
            
            "point_segment": """        # Point visualization: {description}
        point{segment_num} = Dot(axes.c2p({x}, {y}), color={color})
        self.play(ShowCreation(point{segment_num}), run_time={duration})
""",
            
            "line_segment": """        # Line visualization: {description}
        line{segment_num} = Line(axes.c2p({x1}, {y1}), axes.c2p({x2}, {y2}), color={color})
        self.play(ShowCreation(line{segment_num}), run_time={duration})
""",
            
            "transform_segment": """        # Transformation: {description}
        new_graph{segment_num} = axes.get_graph(lambda x: {new_function}, color={color})
        self.play(Transform(graph{base_graph}, new_graph{segment_num}), run_time={duration})
"""
        }
    
    def generate_template_based_code(self, script: str, segments: List[Dict], 
                                   topic: str, job_id: str) -> str:
        """Generate code using safe templates"""
        
        # Create a constrained prompt that forces template usage
        template_prompt = f"""
You are a template-filling expert. You MUST use ONLY the provided templates.

TOPIC: {topic}
SCRIPT: {script}
SEGMENTS: {len(segments)} segments with timing

AVAILABLE SAFE TEMPLATES:
1. TEXT: Display text that matches script
2. GRAPH: Show mathematical function
3. POINT: Show coordinate point  
4. LINE: Show line between points
5. TRANSFORM: Change one graph to another

RULES:
- Use ONLY the template types above
- Fill in simple values: colors (RED, BLUE, GREEN), coordinates (integers), functions (x**2, 2*x, etc.)
- NO complex expressions, NO Tex(), NO external files
- Each segment gets ONE template type

For each segment, choose the best template and provide:
- Template type (TEXT/GRAPH/POINT/LINE/TRANSFORM)
- Simple fill-in values

EXAMPLE OUTPUT:
Segment 1: TEXT, text="Chain rule introduction", duration=3
Segment 2: GRAPH, function="x**2", color="BLUE", duration=2
Segment 3: POINT, x=1, y=1, color="RED", duration=2

Generate template choices for all {len(segments)} segments:
"""
        
        try:
            # Get template choices from LLM
            response = model.generate_content(template_prompt)
            template_choices = response.text
            
            # Parse and build code using templates
            code = self._build_code_from_templates(template_choices, segments, topic)
            
            return code
            
        except Exception as e:
            print(f"Template generation error: {e}")
            return self._create_fallback_code(segments, topic)
    
    def _build_code_from_templates(self, choices: str, segments: List[Dict], topic: str) -> str:
        """Build code from template choices"""
        
        # Parse the LLM choices (simplified parsing)
        segment_code = ""
        
        for i, segment in enumerate(segments, 1):
            duration = segment.get('duration', 2.0)
            text = segment.get('text', f'Segment {i}')[:50]  # Limit text length

            # Clean text for safe usage in Python strings
            clean_text = self._clean_text_for_python(text)

            # Default to safe text + graph pattern
            segment_code += self.safe_templates["text_segment"].format(
                segment_num=i,
                description=f"Script segment {i}",
                text_content=clean_text,
                text_duration=min(duration * 0.6, 3.0)
            )
            
            # Add script-synchronized mathematical visualization
            animation_type = self._choose_animation_for_text(text)

            if animation_type == "parabola":
                segment_code += self.safe_templates["graph_segment"].format(
                    segment_num=i,
                    description="Parabola visualization",
                    function="x**2",
                    color="BLUE",
                    duration=max(duration * 0.4, 2.0)
                )
            elif animation_type == "linear":
                segment_code += self.safe_templates["graph_segment"].format(
                    segment_num=i,
                    description="Linear function",
                    function="2*x + 1",
                    color="GREEN",
                    duration=max(duration * 0.4, 2.0)
                )
            elif animation_type == "point":
                segment_code += self.safe_templates["point_segment"].format(
                    segment_num=i,
                    description="Important point",
                    x=2,
                    y=4,
                    color="RED",
                    duration=max(duration * 0.4, 1.5)
                )
            else:  # Default mathematical content
                if i % 2 == 1:
                    segment_code += self.safe_templates["graph_segment"].format(
                        segment_num=i,
                        description="Mathematical visualization",
                        function="x**2",
                        color="BLUE",
                        duration=max(duration * 0.4, 2.0)
                    )
                else:
                    segment_code += self.safe_templates["point_segment"].format(
                        segment_num=i,
                        description="Mathematical point",
                        x=i % 4,
                        y=(i % 4) ** 2,
                        color="YELLOW",
                        duration=max(duration * 0.4, 1.5)
                    )
        
        # Build final code
        scene_name = f"TemplateScene"
        final_code = self.safe_templates["basic_scene"].format(
            scene_name=scene_name,
            segments=segment_code
        )
        
        return final_code

    def _clean_text_for_python(self, text: str) -> str:
        """Clean text to be safe for Python string literals"""
        # Remove or escape problematic characters
        text = text.replace('"', "'")  # Replace double quotes with single quotes
        text = text.replace('\n', ' ')  # Replace newlines with spaces
        text = text.replace('\r', ' ')  # Replace carriage returns
        text = text.replace('\\', '')   # Remove backslashes

        # Limit length and ensure it's not empty
        text = text.strip()[:40]
        if not text:
            text = "Mathematical concept"

        return text

    def _choose_animation_for_text(self, text: str) -> str:
        """Choose appropriate animation type based on script content"""
        text_lower = text.lower()

        # Check for specific mathematical concepts
        if any(word in text_lower for word in ["parabola", "quadratic", "x squared", "x²", "curve"]):
            return "parabola"
        elif any(word in text_lower for word in ["linear", "straight line", "slope", "y = mx"]):
            return "linear"
        elif any(word in text_lower for word in ["point", "coordinate", "vertex", "intercept", "solution"]):
            return "point"
        elif any(word in text_lower for word in ["equation", "formula", "expression"]):
            return "equation"
        elif any(word in text_lower for word in ["graph", "plot", "function", "draw"]):
            return "parabola"  # Default to parabola for graphing
        else:
            return "default"

    def _create_fallback_code(self, segments: List[Dict], topic: str) -> str:
        """Create safe fallback code if template parsing fails"""
        
        segment_code = ""
        
        for i, segment in enumerate(segments, 1):
            duration = segment.get('duration', 2.0)
            text = segment.get('text', f'Segment {i}')[:30]
            
            # Safe text segment
            segment_code += f"""        # Segment {i}
        text{i} = Text("{text}").to_edge(UP)
        self.play(Write(text{i}), run_time={min(duration, 3.0):.1f})
        self.play(FadeOut(text{i}), run_time=0.5)
        
"""
        
        # Add one safe graph
        segment_code += """        # Mathematical visualization
        graph = axes.get_graph(lambda x: x**2, color=BLUE)
        self.play(ShowCreation(graph), run_time=3)
"""
        
        final_code = self.safe_templates["basic_scene"].format(
            scene_name="FallbackScene",
            segments=segment_code
        )
        
        return final_code


def main():
    """Test template-based generation"""
    
    print("🧪 TESTING TEMPLATE-BASED GENERATION")
    print("=" * 50)
    
    generator = TemplateManimGenerator()
    
    # Test with sample data
    test_script = "The chain rule helps us find derivatives of composite functions."
    test_segments = [
        {"text": "Introduction to chain rule", "duration": 3.0},
        {"text": "Mathematical foundation", "duration": 4.0},
        {"text": "Practical examples", "duration": 3.5}
    ]
    
    code = generator.generate_template_based_code(
        test_script, test_segments, "chain rule", "template_test"
    )
    
    print("✅ Template-based code generated:")
    print(code[:500] + "..." if len(code) > 500 else code)


if __name__ == "__main__":
    main()
