"""
Test the script-synchronized True RAG system
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from complete_true_rag_system import CompleteTrueRAGSystem

def test_synchronized_generation():
    """Test the synchronized script-to-video generation"""
    
    print("🎯 Testing Script-Synchronized Video Generation")
    print("=" * 70)
    print("This test ensures the video animations match the spoken script content")
    print("=" * 70)
    
    # Initialize system
    system = CompleteTrueRAGSystem()
    
    # Test topic
    topic = "derivatives and tangent lines"
    
    print(f"\n🎬 Generating synchronized video for: {topic}")
    print("-" * 50)
    
    try:
        result = system.generate_complete_video(topic, output_format="mp4")
        
        if result['success']:
            print(f"✅ Synchronized generation successful!")
            
            # Show what was generated
            metadata = result['metadata']
            
            print(f"\n📝 Generated Script:")
            print("-" * 30)
            with open(result['script_file'], 'r', encoding='utf-8') as f:
                script = f.read()
            print(script[:300] + "..." if len(script) > 300 else script)
            
            print(f"\n🎬 Generated Code Preview:")
            print("-" * 30)
            with open(result['code_file'], 'r', encoding='utf-8') as f:
                code = f.read()
            
            # Show script comments in the code
            code_lines = code.split('\n')
            for i, line in enumerate(code_lines[:40], 1):
                if 'Sentence' in line and ':' in line:
                    print(f"{i:2d}: {line}")
                elif i <= 10:  # Show first 10 lines regardless
                    print(f"{i:2d}: {line}")
            
            print(f"\n📊 Synchronization Analysis:")
            print("-" * 30)
            
            # Count sentences in script
            script_sentences = [s.strip() for s in script.split('.') if s.strip()]
            print(f"📝 Script sentences: {len(script_sentences)}")
            
            # Count sentence comments in code
            sentence_comments = [line for line in code_lines if 'Sentence' in line and ':' in line]
            print(f"🎬 Code sentence markers: {len(sentence_comments)}")
            
            # Show timing info
            timing = metadata['timing_info']
            print(f"⏱️ Total duration: {timing.get('total_duration', 0):.2f} seconds")
            
            if len(script_sentences) > 0 and len(sentence_comments) > 0:
                print(f"✅ Script-to-code synchronization: IMPLEMENTED")
                
                # Show first few synchronization points
                print(f"\n🎯 Synchronization Points:")
                for i, comment in enumerate(sentence_comments[:5]):
                    sentence_text = comment.split('"')[1] if '"' in comment else "Unknown"
                    print(f"   {i+1}. {sentence_text[:50]}...")
            else:
                print(f"⚠️ Synchronization markers not found")
            
            print(f"\n📹 Final Video: {result['final_video']}")
            
            return result
        else:
            print(f"❌ Generation failed: {result['error']}")
            return None
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_multiple_synchronized_topics():
    """Test synchronization with multiple topics"""
    
    print("\n🎯 Testing Multiple Synchronized Topics")
    print("=" * 70)
    
    system = CompleteTrueRAGSystem()
    
    topics = [
        "vector addition in 2D",
        "simple mathematical concepts"
    ]
    
    results = []
    
    for i, topic in enumerate(topics, 1):
        print(f"\n🎬 Test {i}: {topic}")
        print("-" * 40)
        
        try:
            result = system.generate_complete_video(topic, output_format="mp4")
            
            if result['success']:
                print(f"✅ Generation successful!")
                
                # Quick synchronization check
                with open(result['script_file'], 'r', encoding='utf-8') as f:
                    script = f.read()
                with open(result['code_file'], 'r', encoding='utf-8') as f:
                    code = f.read()
                
                script_sentences = len([s.strip() for s in script.split('.') if s.strip()])
                code_markers = len([line for line in code.split('\n') if 'Sentence' in line])
                
                print(f"📝 Script sentences: {script_sentences}")
                print(f"🎬 Code markers: {code_markers}")
                print(f"📹 Video: {result['final_video']}")
                
                results.append(result)
            else:
                print(f"❌ Generation failed: {result['error']}")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return results

def analyze_synchronization_quality(result):
    """Analyze the quality of script-video synchronization"""
    
    print(f"\n🔍 Analyzing Synchronization Quality")
    print("=" * 50)
    
    if not result:
        print("❌ No result to analyze")
        return
    
    try:
        # Read script and code
        with open(result['script_file'], 'r', encoding='utf-8') as f:
            script = f.read()
        with open(result['code_file'], 'r', encoding='utf-8') as f:
            code = f.read()
        
        # Analyze script
        script_sentences = [s.strip() for s in script.split('.') if s.strip()]
        script_words = len(script.split())
        
        # Analyze code
        code_lines = code.split('\n')
        sentence_markers = [line for line in code_lines if 'Sentence' in line and ':' in line]
        wait_commands = [line for line in code_lines if 'self.wait(' in line]
        play_commands = [line for line in code_lines if 'self.play(' in line]
        
        print(f"📊 Synchronization Analysis:")
        print(f"   Script sentences: {len(script_sentences)}")
        print(f"   Script words: {script_words}")
        print(f"   Code sentence markers: {len(sentence_markers)}")
        print(f"   Animation commands: {len(play_commands)}")
        print(f"   Wait commands: {len(wait_commands)}")
        
        # Calculate synchronization ratio
        if len(script_sentences) > 0:
            sync_ratio = len(sentence_markers) / len(script_sentences)
            print(f"   Synchronization ratio: {sync_ratio:.2f}")
            
            if sync_ratio >= 0.8:
                print("✅ Excellent synchronization")
            elif sync_ratio >= 0.5:
                print("👍 Good synchronization")
            else:
                print("⚠️ Poor synchronization")
        
        # Show timing analysis
        metadata = result['metadata']
        timing = metadata['timing_info']
        total_duration = timing.get('total_duration', 0)
        
        if total_duration > 0:
            words_per_minute = (script_words / total_duration) * 60
            print(f"   Speaking rate: {words_per_minute:.1f} words/minute")
            
            if 120 <= words_per_minute <= 180:
                print("✅ Natural speaking pace")
            else:
                print("⚠️ Speaking pace may be too fast/slow")
        
        # Show sample synchronization points
        print(f"\n🎯 Sample Synchronization Points:")
        for i, marker in enumerate(sentence_markers[:3]):
            if '"' in marker:
                sentence_text = marker.split('"')[1]
                print(f"   {i+1}. {sentence_text[:60]}...")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

def main():
    """Main test function"""
    
    print("🚀 Testing Script-Synchronized True RAG System")
    print("=" * 80)
    print("This test verifies that video animations are synchronized with script content")
    print("=" * 80)
    
    # Test 1: Single synchronized generation
    result = test_synchronized_generation()
    
    # Test 2: Analyze synchronization quality
    if result:
        analyze_synchronization_quality(result)
    
    # Test 3: Multiple topics
    results = test_multiple_synchronized_topics()
    
    # Summary
    print(f"\n🎉 Synchronization Test Summary")
    print("=" * 50)
    
    if result:
        print(f"✅ Primary test: SUCCESS")
        print(f"📹 Video: {result['final_video']}")
    else:
        print(f"❌ Primary test: FAILED")
    
    print(f"✅ Additional tests: {len(results)}/{len(['vector addition in 2D', 'simple mathematical concepts'])}")
    
    if result or results:
        print(f"\n🎯 Key Improvements:")
        print(f"✅ Script content drives video generation")
        print(f"✅ Sentence-by-sentence synchronization")
        print(f"✅ Timing-aware animation pacing")
        print(f"✅ Context-aware mathematical content")
        print(f"\n🎬 The True RAG system now generates videos that match the spoken content!")

if __name__ == "__main__":
    main()
