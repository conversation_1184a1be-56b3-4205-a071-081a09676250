"""
Audio-First Synchronized Video Generator
1. Generate natural script
2. Create audio at natural pace  
3. Generate video code that matches audio timing
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, List, Tuple
import google.generativeai as genai
from dotenv import load_dotenv

# Import existing modules
from .rag_system import ManimRAG
from .synchronized_tts import SynchronizedTTS
from .tts import extract_sentences
from .template_based_generator import TemplateManimGenerator

load_dotenv()

# Try primary API key first, fallback to secondary if quota exceeded
primary_key = os.getenv("GOOGLE_API_KEY2")
backup_key = os.getenv("GOOGLE_API_KEY")
current_api_key = primary_key

genai.configure(api_key=current_api_key)


class AudioFirstGenerator:
    """Generates video synchronized to natural audio timing"""
    
    def __init__(self):
        self.model = genai.GenerativeModel("gemini-1.5-flash")
        self.rag = None
        self.tts = SynchronizedTTS()
        self.template_generator = TemplateManimGenerator()

        # Initialize RAG if available
        if ManimRAG:
            try:
                self.rag = ManimRAG()
                self.rag.setup()
                print("✅ RAG system initialized for audio-first generation")
            except Exception as e:
                print(f"⚠️ RAG system failed to initialize: {e}")
                self.rag = None

        print("✅ Template-based generator initialized for reliable code generation")
    
    def generate_audio_first_content(self, prompt: str, job_id: str) -> Dict:
        """Generate synchronized content starting with natural audio"""
        
        print("🎯 Audio-First Generation Pipeline")
        print("=" * 50)
        
        # Step 1: Generate natural educational script
        print("📝 Step 1: Generating natural educational script...")
        script = self._generate_natural_script(prompt)
        print(f"✅ Script generated ({len(script)} characters)")
        
        # Step 2: Create audio at natural pace
        print("🎵 Step 2: Creating audio at natural speaking pace...")
        audio_path, audio_timing = self._create_natural_audio(script, job_id)
        print(f"✅ Audio created: {audio_path}")
        print(f"📊 Natural duration: {audio_timing['total_duration']:.2f} seconds")
        
        # Step 3: Generate video code with enhanced manim animation prompts
        print("🎬 Step 3: Generating rich manim animation code...")
        video_code = self._generate_rich_manim_animations(
            prompt, script, audio_timing["segments"], audio_timing["total_duration"]
        )
        print("✅ Synchronized video code generated")
        
        # Save all content
        result = {
            "script": script,
            "manim_code": video_code,
            "audio_path": audio_path,
            "audio_timing": audio_timing,
            "natural_duration": audio_timing['total_duration']
        }
        
        self._save_audio_first_content(result, job_id)
        
        return result
    
    def _generate_natural_script(self, prompt: str) -> str:
        """Generate a natural educational script without timing constraints"""
        
        # Get relevant examples from RAG
        relevant_examples = ""
        if self.rag:
            try:
                search_terms = self._extract_search_terms(prompt)
                relevant_examples = self.rag.get_relevant_examples(search_terms)
            except Exception as e:
                print(f"Error getting RAG examples: {e}")
        
        examples_section = ""
        if relevant_examples:
            examples_section = f"RELEVANT EXAMPLES FROM 3BLUE1BROWN:\n{relevant_examples}\n"
        
        script_prompt = f"""
You are an expert educational content creator in the style of 3Blue1Brown.
Generate a natural, engaging script for explaining: {prompt}

SCRIPT REQUIREMENTS:
1. Write in a conversational, educational tone
2. Use clear, simple language suitable for learning
3. Include natural pauses and transitions
4. Structure content logically with smooth flow
5. Aim for natural speaking pace (150-180 words per minute)
6. Include engaging explanations and insights
7. Make it suitable for visual animation

CONTENT STRUCTURE:
- Start with an engaging introduction
- Build concepts step by step
- Use analogies and examples where helpful
- End with a clear conclusion or insight

{examples_section}

TOPIC: {prompt}

Generate ONLY the script text - no code, no timing markers, just natural educational narration.
Make it engaging and suitable for a 3Blue1Brown style animated explanation.
"""

        try:
            response = self.model.generate_content(script_prompt)
            script = response.text.strip()

            # Clean up the script
            script = self._clean_script(script)

            return script

        except Exception as e:
            error_str = str(e)
            print(f"Error generating script: {e}")

            # Check if it's a quota error and try backup key
            if "429" in error_str and "quota" in error_str.lower():
                print("🔄 Quota exceeded, switching to backup API key...")
                return self._retry_with_backup_key(script_prompt, "script", prompt)

            # Fallback script for other errors
            return f"Let's explore {prompt}. This is a fundamental concept in mathematics that helps us understand how things change and relate to each other. We'll visualize this step by step to build intuition."
    
    def _create_natural_audio(self, script: str, job_id: str) -> Tuple[str, Dict]:
        """Create audio at natural speaking pace and return timing info"""
        
        # Create audio without duration constraints
        audio_path = self.tts.generate_timed_audio(
            script=script,
            job_id=job_id,
            timing_info=None,
            target_duration=None  # No target - let it be natural
        )
        
        # Load timing analysis
        timing_file = Path(f"generated/{job_id}/audio_timing.json")
        if timing_file.exists():
            with open(timing_file, 'r') as f:
                audio_timing = json.load(f)
                # Ensure consistent key naming
                if 'total_duration_seconds' in audio_timing:
                    audio_timing['total_duration'] = audio_timing['total_duration_seconds']
        else:
            # Fallback timing calculation
            sentences = extract_sentences(script)
            duration = len(sentences) * 3.0
            audio_timing = {
                "total_duration_seconds": duration,
                "total_duration": duration,
                "sentence_count": len(sentences),
                "segments": []
            }
        
        return audio_path, audio_timing
    
    def _generate_synchronized_video_code(self, prompt: str, script: str, 
                                        audio_timing: Dict) -> str:
        """Generate manim code synchronized to audio timing"""
        
        total_duration = audio_timing.get('total_duration', audio_timing.get('total_duration_seconds', 30.0))
        segments = audio_timing.get('segments', [])
        
        # Get relevant examples from RAG + add synthetic perfect examples
        relevant_examples = ""
        if self.rag:
            try:
                search_terms = self._extract_search_terms(prompt)
                relevant_examples = self.rag.get_relevant_examples(search_terms)
            except Exception as e:
                print(f"Error getting RAG examples: {e}")

        # Add perfect synthetic examples for common patterns
        perfect_examples = self._get_perfect_examples()

        examples_section = ""
        if relevant_examples or perfect_examples:
            examples_section = f"""
RELEVANT 3BLUE1BROWN EXAMPLES:
{relevant_examples}

PERFECT SYNTAX EXAMPLES (MANDATORY PATTERNS):
{perfect_examples}
"""
        
        # Create timing breakdown for the prompt
        timing_breakdown = self._create_timing_breakdown(segments)
        
        video_prompt = f"""
🚨 SCRIPT-SYNCHRONIZED VIDEO GENERATION

You MUST create animations that EXACTLY match the script content and timing.

SCRIPT CONTENT:
{script}

AUDIO TIMING: {total_duration:.2f} seconds, {len(segments)} segments
TIMING BREAKDOWN: {timing_breakdown}

🎯 CRITICAL REQUIREMENTS:
1. Each animation segment MUST match the corresponding script sentence
2. Text on screen MUST be the exact words being spoken (or close paraphrase)
3. Mathematical animations MUST demonstrate what's being explained
4. Timing MUST match the audio segments exactly

SCRIPT-TO-ANIMATION MAPPING:
For each sentence in the script, create:
1. Text display showing what's being said
2. Mathematical animation demonstrating the concept
3. Perfect timing synchronization

EXAMPLE SCRIPT-BASED GENERATION:
If script says: "Let's visualize this with a simple curve"
→ Text: "Let's visualize this with a simple curve"
→ Animation: Show curve being drawn
→ Timing: Match the audio duration for this sentence

If script says: "We can draw a secant line connecting two points"
→ Text: "Draw a secant line connecting two points"
→ Animation: Show two points, then secant line connecting them
→ Timing: Match the audio duration

MANDATORY TEMPLATE:
```python
from manimlib import *

class TopicScene(Scene):
    def construct(self):
        # Setup
        axes = Axes(x_range=(-5, 5, 1), y_range=(-5, 5, 1))
        self.play(ShowCreation(axes), run_time=2)

        # Segment 1: [First sentence from script]
        text1 = Text("[Exact words from script]").to_edge(UP)
        self.play(Write(text1), run_time=[segment_duration])
        self.play(FadeOut(text1), run_time=0.5)
        # Animation that demonstrates what's being said
        [mathematical_animation_matching_script]

        # Segment 2: [Second sentence from script]
        text2 = Text("[Exact words from script]").to_edge(UP)
        self.play(Write(text2), run_time=[segment_duration])
        self.play(FadeOut(text2), run_time=0.5)
        # Animation that demonstrates what's being said
        [mathematical_animation_matching_script]
```

AUDIO TIMING: {total_duration:.2f} seconds, {len(segments)} segments
TIMING BREAKDOWN: {timing_breakdown}
SCRIPT: {script}

🎬 RICH MATHEMATICAL ANIMATION REQUIREMENTS:

🚨 CRITICAL: EVERY SEGMENT MUST HAVE RICH MATHEMATICAL CONTENT!

📋 MANDATORY ANIMATION RULES:
1. 🎯 TEXT: Brief explanatory text (2-3 seconds max, then FADE OUT)
2. 🎯 MATH ANIMATIONS: Complex mathematical visualizations for EVERY concept
3. 🎯 NO EMPTY SEGMENTS: Every segment must show mathematical objects
4. 🎯 VISUAL VARIETY: Use graphs, transformations, geometric shapes, moving objects
5. 🎯 EDUCATIONAL VALUE: Animations must demonstrate the mathematical concept being explained

🎨 ENHANCED MATHEMATICAL ANIMATION PATTERNS:

FOR FUNCTIONS/EQUATIONS (match script content):
- If script mentions "parabola" or "quadratic": graph = axes.get_graph(lambda x: x**2, color=BLUE)
- If script mentions "linear": graph = axes.get_graph(lambda x: 2*x + 1, color=GREEN)
- If script mentions "transformation": new_graph = axes.get_graph(lambda x: 2*x**2, color=RED)
- If script mentions "roots" or "solutions": Show x-intercepts with dots
- If script mentions "vertex": Show vertex point with special highlighting
- Animate function drawing: self.play(ShowCreation(graph), run_time=3)
- Show transformations: self.play(Transform(graph, new_graph), run_time=3)

FOR GEOMETRIC CONCEPTS:
- Create shapes: circle = Circle(radius=2, color=BLUE)
- Show construction: self.play(ShowCreation(circle), run_time=2)
- Demonstrate properties: radius_line = Line(circle.get_center(), circle.get_boundary_point(RIGHT))

FOR ALGEBRAIC CONCEPTS (match script mentions):
- If script mentions "equation": Show equation text: eq = Text("x² + 5x + 6 = 0")
- If script mentions "factoring": Show step-by-step: Text("(x + 2)(x + 3) = 0")
- If script mentions "solutions": Show x-intercepts: Dot(axes.c2p(-2, 0)), Dot(axes.c2p(-3, 0))
- If script mentions "completing square": Show algebraic steps progressively

FOR POINTS AND COORDINATES (script-synchronized):
- If script mentions "point": point = Dot(axes.c2p(2, 4), color=RED)
- If script mentions "movement" or "trajectory": Show point moving along path
- If script mentions "intersection": Show multiple points where curves meet
- If script mentions "vertex": Highlight the vertex of parabola with special dot

🎯 SEGMENT-BY-SEGMENT ANIMATION STRATEGY:
Each segment should have MATHEMATICAL CONTENT, not just text:

� SCRIPT-BASED ANIMATION EXAMPLES:

EXAMPLE 1 - Script: "Imagine you're throwing a ball"
✅ TEXT: "Imagine you're throwing a ball"
✅ ANIMATION: Show parabolic trajectory with moving dot
✅ TIMING: Match exact audio duration

EXAMPLE 2 - Script: "It follows a curve – a parabola"
✅ TEXT: "It follows a curve – a parabola"
✅ ANIMATION: Show parabola being drawn: axes.get_graph(lambda x: -x**2 + 4*x)
✅ TIMING: Match exact audio duration

EXAMPLE 3 - Script: "Let's look at x squared plus 5x plus 6"
✅ TEXT: "x squared plus 5x plus 6"
✅ ANIMATION: Show equation text AND parabola graph simultaneously
✅ TIMING: Match exact audio duration

EXAMPLE 4 - Script: "Can we find two numbers that multiply to 6"
✅ TEXT: "Find two numbers that multiply to 6"
✅ ANIMATION: Show factors appearing: Text("2 × 3 = 6")
✅ TIMING: Match exact audio duration

EXAMPLE 3 - Script: "The slope is rise over run"
✅ TEXT: "The slope is rise over run"
✅ ANIMATION: Show right triangle with rise/run labels
✅ TIMING: Match exact audio duration

EXAMPLE 4 - Script: "As we shrink the distance, approaching zero"
✅ TEXT: "Shrinking distance, approaching zero"
✅ ANIMATION: Show two points getting closer, secant becoming tangent
✅ TIMING: Match exact audio duration

🚨 CRITICAL SCRIPT MATCHING RULES:
1. Read each sentence in the script carefully
2. Create text that matches what's being said
3. Create animation that demonstrates the concept being explained
4. Use exact timing from audio segments
5. Make animations visually represent the spoken words

✅ PERFECT RICH ANIMATION SEGMENT (DERIVATIVES EXAMPLE):
# Segment: "The derivative gives us the slope of the tangent line"
explanation = Text("Derivative = Slope").to_edge(UP)
self.play(Write(explanation), run_time=2)
self.play(FadeOut(explanation), run_time=0.5)

# Rich mathematical demonstration
curve = axes.get_graph(lambda x: x**2, color=BLUE)
point = Dot(axes.c2p(1, 1), color=RED)
tangent_line = Line(axes.c2p(0, -1), axes.c2p(2, 3), color=YELLOW)
slope_triangle = Polygon(axes.c2p(1, 1), axes.c2p(2, 1), axes.c2p(2, 3), color=GREEN)

self.play(ShowCreation(curve), run_time=1)
self.play(ShowCreation(point), run_time=1)
self.play(ShowCreation(tangent_line), run_time=1)
self.play(ShowCreation(slope_triangle), run_time=1)

# Show tangent line moving along curve
for x_val in [1.5, 2, 2.5]:
    new_point = Dot(axes.c2p(x_val, x_val**2), color=RED)
    new_tangent = Line(axes.c2p(x_val-0.5, x_val**2-x_val), axes.c2p(x_val+0.5, x_val**2+x_val), color=YELLOW)
    self.play(Transform(point, new_point), Transform(tangent_line, new_tangent), run_time=1)

✅ PERFECT RICH ANIMATION SEGMENT (FUNCTION TRANSFORMATIONS):
# Segment: "Changing the coefficient affects the parabola's shape"
explanation = Text("Coefficient Changes Shape").to_edge(UP)
self.play(Write(explanation), run_time=2)
self.play(FadeOut(explanation), run_time=0.5)

# Show multiple transformations with visual comparison
graph1 = axes.get_graph(lambda x: x**2, color=BLUE)
graph2 = axes.get_graph(lambda x: 2*x**2, color=GREEN)
graph3 = axes.get_graph(lambda x: 0.5*x**2, color=RED)

self.play(ShowCreation(graph1), run_time=1)
label1 = Text("y = x²").next_to(graph1, UP)
self.play(Write(label1), run_time=1)

self.play(ShowCreation(graph2), run_time=1)
label2 = Text("y = 2x²").next_to(graph2, UP+RIGHT)
self.play(Write(label2), run_time=1)

self.play(ShowCreation(graph3), run_time=1)
label3 = Text("y = 0.5x²").next_to(graph3, UP+LEFT)
self.play(Write(label3), run_time=1)

# Animate between them to show the effect
self.play(Transform(graph1, graph2), run_time=2)
self.play(Transform(graph1, graph3), run_time=2)

EXAMPLE GOOD SEGMENT:
# Segment: "Let's see how the parabola changes when we modify the coefficient"
graph1 = axes.get_graph(lambda x: x**2, color=BLUE)
graph2 = axes.get_graph(lambda x: 2*x**2, color=GREEN)
graph3 = axes.get_graph(lambda x: 0.5*x**2, color=YELLOW)
self.play(ShowCreation(graph1), run_time=2)
self.play(Transform(graph1, graph2), run_time=2)  # Show narrowing
self.play(Transform(graph1, graph3), run_time=2)  # Show widening

EXAMPLE BAD SEGMENT (AVOID):
# Segment: "Parabolas are important"
text = Text("Parabolas are important")
self.play(Write(text), run_time=5)
self.play(FadeOut(text), run_time=1)

4. TEXT MANAGEMENT:
   # Brief labels only
   label = Text("Slope = 2").next_to(graph1, UP)
   self.play(Write(label), run_time=1)
   self.play(FadeOut(label), run_time=0.5)  # Remove quickly

CRITICAL SYNTAX RULES - FOLLOW EXACTLY:
- from manimlib import *
- class YourScene(Scene):
- axes = Axes(x_range=(-3, 3, 1), y_range=(-2, 8, 1))
- graph = axes.get_graph(lambda x: x**2, color=BLUE)
- Use ShowCreation(), Write(), Transform(), FadeIn(), FadeOut()
- Use axes.c2p(x, y) for coordinates
- Text() for labels (keep brief)

ABSOLUTELY FORBIDDEN - WILL CAUSE ERRORS:
- NEVER use Tex(), MathTex(), TexMobject() - ONLY use Text()
- NEVER use invalid decimal literals like 1.5.0 or 2..3
- NEVER use HTML tags like <sup>, <sub>, <b>
- NEVER use external files (.png, .svg, ImageMobject, SVGMobject)
- NEVER use coords_to_point() - use axes.c2p()
- NEVER use Create() - use ShowCreation()
- NEVER use GRAY - use GREY
- NEVER use Transform() with single argument - requires Transform(source, target)

🚨 CRITICAL SYNTAX RULES - FOLLOW EXACTLY (COMMON ERRORS):

1. POINT/COORDINATE ANIMATION (MOST COMMON ERROR):
   ❌ NEVER DO: FadeOut(axes.c2p(1, 2))
   ❌ NEVER DO: FadeIn(axes.c2p(1, 2))
   ❌ NEVER DO: ShowCreation(axes.c2p(1, 2))
   ✅ ALWAYS DO: dot = Dot(axes.c2p(1, 2), color=RED)
                 self.play(FadeOut(dot))

2. POINT CREATION PATTERNS:
   ✅ CORRECT: point1 = Dot(axes.c2p(1, 1), color=RED)
   ✅ CORRECT: point2 = Dot(axes.c2p(2, 4), color=BLUE)
   ✅ CORRECT: self.play(ShowCreation(point1), ShowCreation(point2))
   ✅ CORRECT: self.play(FadeIn(point1), FadeIn(point2))
   ✅ CORRECT: self.play(FadeOut(point1), FadeOut(point2))

3. COLOR NAMING (COMMON ERROR):
   ❌ NEVER USE: GRAY
   ✅ ALWAYS USE: GREY
   Example: circle = Circle(radius=1, color=GREY)

4. TEXT AND MATH DISPLAY (CRITICAL - MOST COMMON ERROR):
   ❌ NEVER USE: Tex(), MathTex(), TexMobject()
   ✅ ALWAYS USE: Text() for ALL text and math

   COMMON MATH EXPRESSIONS:
   ❌ WRONG: Tex("$\\frac{dy}{dx}$", font_size=72)
   ✅ CORRECT: Text("dy/dx", font_size=72)
   ❌ WRONG: Tex("$\\frac{ds}{dt}$")
   ✅ CORRECT: Text("ds/dt", font_size=36)
   ❌ WRONG: MathTex("x^2 + 1")
   ✅ CORRECT: Text("x² + 1", font_size=36)
   ❌ WRONG: Tex("$f'(x)$")
   ✅ CORRECT: Text("f'(x)", font_size=36)

5. OTHER CRITICAL RULES:
   - Decimals: Write 1.5 NOT 1.5.0 or 1..5 (CAUSES SYNTAX ERROR)
   - Transform: NEVER use single argument
     ❌ WRONG: Transform(new_graph)
     ✅ CORRECT: Transform(old_graph, new_graph)
   - Strings: Always close quotes properly
   - Commas: Required between function arguments
   - Parentheses: Must be balanced ( ) [ ] and braces

COMPLETE WORKING EXAMPLE (COPY THIS PATTERN):
```python
from manimlib import *

class ExampleScene(Scene):
    def construct(self):
        # Segment 1: Setup (3 seconds)
        axes = Axes(x_range=(-3, 3, 1), y_range=(-2, 8, 1))
        self.play(ShowCreation(axes), run_time=3)

        # Segment 2: First graph (4 seconds)
        graph1 = axes.get_graph(lambda x: x**2, color=BLUE)
        label1 = Text("y = x²").next_to(axes, UP)
        self.play(ShowCreation(graph1), Write(label1), run_time=4)

        # Segment 3: Add points (3 seconds)
        point1 = Dot(axes.c2p(1, 1), color=RED)
        point2 = Dot(axes.c2p(2, 4), color=RED)
        self.play(ShowCreation(point1), ShowCreation(point2), run_time=3)

        # Segment 4: Transform graph (5 seconds)
        graph2 = axes.get_graph(lambda x: 2*x**2, color=GREEN)
        label2 = Text("y = 2x²").next_to(axes, UP)
        self.play(Transform(graph1, graph2), Transform(label1, label2), run_time=5)

        # Segment 5: Clean up (2 seconds)
        self.play(FadeOut(point1), FadeOut(point2), FadeOut(label1), run_time=2)
```

FINAL CHECKLIST BEFORE GENERATING:
✓ Use ONLY valid decimals: 1.5, 2.0, 3.14 (NO 1.5.0 or 2..3)
✓ Create Dot objects for points: dot = Dot(axes.c2p(x,y))
✓ Use Transform with TWO arguments: Transform(old_obj, new_obj)
✓ Close all strings properly: "text"
✓ Use Text() NEVER Tex() or MathTex()
✓ Store graphs in variables before animating
✓ Match timing: {total_duration:.2f} seconds total
✓ Create animations for ALL {len(segments)} segments

TOPIC: {prompt}

{examples_section}

Generate SYNTACTICALLY PERFECT code following the examples above EXACTLY.
"""

        # Try generation with validation and retry logic - NO FALLBACK CODE
        max_attempts = 5  # Increased attempts since we're not using fallback

        for attempt in range(max_attempts):
            try:
                response = self.model.generate_content(video_prompt)
                code = response.text.strip()

                # Clean the code
                code = self._clean_manim_code(code)

                # Validate the generated manim code
                is_valid, error_msg = self._validate_manim_code(code)

                if is_valid:
                    print(f"✅ Code validation passed on attempt {attempt + 1}")
                    return code
                else:
                    print(f"⚠️ Code validation failed on attempt {attempt + 1}")
                    print(f"🔍 Errors found: {error_msg}")

                    if attempt < max_attempts - 1:
                        # Add enhanced error feedback to the original prompt for regeneration
                        error_feedback = f"""

🚨 CRITICAL ERRORS DETECTED IN PREVIOUS ATTEMPT - MUST AVOID:

SPECIFIC ERRORS FOUND:
{error_msg}

🔧 MANDATORY FIXES FOR NEXT GENERATION:

🚨 COORDINATE ASSIGNMENT ERRORS (MOST COMMON):
- NEVER write: circle = axes.c2p(1, 2)
- ALWAYS write: circle = Circle(radius=1, color=BLUE) OR circle = Dot(axes.c2p(1, 2), color=BLUE)
- NEVER write: line1 = axes.c2p(1, 2)
- ALWAYS write: line1 = Line(axes.c2p(0, 0), axes.c2p(1, 2), color=BLUE)

🚨 ANIMATION ERRORS (CRITICAL - MOST COMMON):
- NEVER animate coordinates directly:
  ❌ FadeOut(axes.c2p(1,2))
  ❌ FadeIn(axes.c2p(1,2))
  ❌ ShowCreation(axes.c2p(1,2))
- ALWAYS create Dot objects first:
  ✅ dot = Dot(axes.c2p(1,2), color=RED)
  ✅ self.play(FadeOut(dot))
  ✅ self.play(FadeIn(dot))
  ✅ self.play(ShowCreation(dot))

🚨 COLOR ERRORS:
- NEVER use: GRAY
- ALWAYS use: GREY
- Example: circle = Circle(radius=1, color=GREY)

🚨 TEXT AND MATH ERRORS (CRITICAL):
- NEVER use: Tex(), MathTex(), TexMobject()
- ALWAYS use: Text() for ALL text and math
- Example: Text("dy/dx", font_size=72) NOT Tex("$\\frac{dy}{dx}$", font_size=72)
- Example: Text("f'(x)") NOT Tex("$f'(x)$")
- NO LaTeX expressions: Use simple text with Unicode symbols

🚨 SYNTAX ERRORS:
- Use valid decimals: 1.5 NOT 1.5.0
- Close all strings: "text" NOT "text
- Use proper indentation: 4 spaces inside construct()

✅ GENERATE COMPLETELY NEW CODE that avoids ALL the above errors.
✅ Focus on RICH MATHEMATICAL ANIMATIONS with proper text explanations.
✅ Use the template structure and follow the examples exactly.
"""

                        # Add error feedback to the original prompt for regeneration
                        video_prompt += error_feedback
                        print(f"🔄 Regenerating with enhanced error feedback (attempt {attempt + 2})...")
                        continue
                    else:
                        print("❌ Max attempts reached - validation still failing")
                        print("🚨 CRITICAL: Unable to generate valid code after all attempts")
                        raise Exception(f"Code validation failed after {max_attempts} attempts: {error_msg}")

            except Exception as e:
                error_str = str(e)
                print(f"❌ Error generating video code on attempt {attempt + 1}: {e}")

                # Check if it's a quota error and try backup key
                if "429" in error_str and "quota" in error_str.lower():
                    print("🔄 Quota exceeded, switching to backup API key...")
                    backup_result = self._retry_with_backup_key(video_prompt, "video", prompt, total_duration)
                    if backup_result:
                        return backup_result

                # If not quota error or backup failed, continue to next attempt
                if attempt < max_attempts - 1:
                    print(f"🔄 Retrying after error (attempt {attempt + 2})...")
                    continue
                else:
                    print("❌ All attempts failed due to errors")
                    raise Exception(f"Video generation failed after {max_attempts} attempts: {e}")
    
    def _create_timing_breakdown(self, segments: List[Dict]) -> str:
        """Create a detailed timing breakdown for the LLM with animation suggestions"""
        if not segments:
            return "No detailed timing available"

        breakdown = []
        for i, segment in enumerate(segments):
            start = segment.get('start_time', i * 3)
            duration = segment.get('duration', 3)
            end = start + duration
            text = segment.get('text', f'Segment {i+1}')

            # Truncate text but keep it meaningful
            display_text = text[:60] + "..." if len(text) > 60 else text

            # Add animation suggestions based on content
            animation_hint = self._suggest_animation_for_text(text)

            breakdown.append(
                f"  Segment {i+1}: {start:.1f}s - {end:.1f}s ({duration:.1f}s)\n"
                f"    Audio: \"{display_text}\"\n"
                f"    Animation: {animation_hint}\n"
            )

        return "\n".join(breakdown)

    def _validate_manim_code(self, code: str) -> tuple[bool, str]:
        """Enhanced comprehensive validation for 3Blue1Brown manim code"""
        errors = []

        # Check basic syntax compilation
        try:
            compile(code, '<string>', 'exec')
        except SyntaxError as e:
            errors.append(f"SYNTAX ERROR: {e}")

        # Check for required imports
        if "from manimlib import *" not in code:
            errors.append("MISSING IMPORT: Must include 'from manimlib import *'")

        # Check for incorrect imports
        if "from manim import" in code:
            errors.append("WRONG IMPORT: Use 'from manimlib import *' not 'from manim import'")

        # Check for Scene class
        if "class " not in code or "(Scene)" not in code:
            errors.append("MISSING CLASS: Must have class that inherits from Scene")

        # Check for construct method
        if "def construct(self):" not in code:
            errors.append("MISSING METHOD: Must have def construct(self): method")

        # Check for 3Blue1Brown specific issues - CRITICAL ERRORS
        if "MathTex(" in code:
            errors.append("FORBIDDEN: MathTex() not available in 3Blue1Brown manim - use Text() only")
        if "TexMobject(" in code:
            errors.append("FORBIDDEN: TexMobject() not available in 3Blue1Brown manim - use Text() only")
        if "Tex(" in code:
            errors.append("FORBIDDEN: Tex() not available in 3Blue1Brown manim - use Text() only")

        # Check for coordinate method issues
        if "coords_to_point(" in code:
            errors.append("WRONG METHOD: Use axes.c2p() instead of coords_to_point()")

        # Check for incorrect animation methods
        if "Create(" in code:
            errors.append("WRONG ANIMATION: Use ShowCreation() instead of Create()")
        if "ReplacementTransform(" in code:
            errors.append("WRONG ANIMATION: Use Transform() instead of ReplacementTransform()")

        # Check for color issues
        if "GRAY" in code:
            errors.append("WRONG COLOR: Use GREY instead of GRAY")

        # Check for balanced parentheses/brackets
        if code.count("(") != code.count(")"):
            errors.append("SYNTAX ERROR: Unbalanced parentheses")
        if code.count("[") != code.count("]"):
            errors.append("SYNTAX ERROR: Unbalanced square brackets")
        if code.count("{") != code.count("}"):
            errors.append("SYNTAX ERROR: Unbalanced curly braces")

        # Check for common 3b1b patterns
        if "FunctionGraph(" in code and "axes.get_graph(" not in code:
            errors.append("WRONG PATTERN: Use axes.get_graph() instead of FunctionGraph()")

        # Check for external file references - FORBIDDEN
        if "ImageMobject(" in code:
            errors.append("FORBIDDEN: ImageMobject requires external files - use Text() or geometric shapes")
        if "SVGMobject(" in code:
            errors.append("FORBIDDEN: SVGMobject requires external files - use geometric shapes or Text()")
        if '".png"' in code or '".jpg"' in code or '".jpeg"' in code or '".gif"' in code:
            errors.append("FORBIDDEN: Image file references not allowed - use built-in shapes and Text()")
        if '".svg"' in code:
            errors.append("FORBIDDEN: SVG file references not allowed - use built-in shapes")

        # Check for HTML-like syntax in Text
        if '<sup>' in code or '</sup>' in code:
            errors.append("FORBIDDEN: HTML <sup> tags not supported - use ^ for exponents")
        if '<sub>' in code or '</sub>' in code:
            errors.append("FORBIDDEN: HTML <sub> tags not supported - use plain text")
        if any(tag in code for tag in ['<b>', '</b>', '<i>', '</i>', '<u>', '</u>']):
            errors.append("FORBIDDEN: HTML formatting tags not supported - use plain text")

        # Enhanced decimal literal checking
        import re
        invalid_decimals = re.findall(r'\d+\.\d*\.\d*|\d+\.\.\d*|\d+\.\.|\.\.\d+', code)
        if invalid_decimals:
            errors.append(f"SYNTAX ERROR: Invalid decimal literals {invalid_decimals}. Use 1.5 NOT 1.5.0 or 1..5")

        # Enhanced Tex checking
        if re.search(r'\bTex\s*\(', code):
            errors.append("FORBIDDEN: Tex() not available in 3Blue1Brown manim - use Text() only")

        # Enhanced Transform checking
        transform_matches = re.findall(r'Transform\s*\(\s*([^,)]+)\s*\)', code)
        if transform_matches:
            errors.append(f"WRONG USAGE: Transform() requires TWO arguments: Transform(source, target). Found single argument: {transform_matches}")

        # CRITICAL: Enhanced coordinate animation checking (most common error)
        coord_patterns = [
            r'(FadeOut|FadeIn|ShowCreation|Write|Transform)\s*\(\s*axes\.c2p\([^)]+\)\s*\)',
            r'self\.play\s*\(\s*axes\.c2p\([^)]+\)\s*\)',
            r'(FadeOut|FadeIn|ShowCreation|Write)\s*\(\s*[^,)]*\.c2p\([^)]+\)\s*\)'
        ]

        for pattern in coord_patterns:
            coord_animations = re.findall(pattern, code)
            if coord_animations:
                errors.append(f"CRITICAL ERROR: Cannot animate coordinates directly! Found: {coord_animations}. Must create Dot objects first!")

        # Enhanced line-by-line validation
        lines = code.split('\n')
        variables = {}
        in_class = False
        in_construct = False

        for i, line in enumerate(lines, 1):
            original_line = line
            line = line.strip()

            # Track code structure
            if line.startswith('class ') and '(Scene)' in line:
                in_class = True
            elif line.startswith('def construct(self):'):
                in_construct = True

            # Track variable assignments with enhanced detection
            if '=' in line and not line.startswith('#') and not line.startswith('def '):
                parts = line.split('=', 1)
                if len(parts) == 2:
                    var_name = parts[0].strip()
                    assignment = parts[1].strip()

                    # CRITICAL: Check for coordinate assignment to variables
                    if 'axes.c2p(' in assignment and 'Dot(' not in assignment and 'Line(' not in assignment:
                        # This is assigning coordinates directly to a variable - FORBIDDEN
                        errors.append(f"Line {i}: CRITICAL ERROR - Cannot assign coordinates to variable '{var_name}'. Use: {var_name} = Dot(axes.c2p(...)) or {var_name} = Line(axes.c2p(...), axes.c2p(...))")
                        variables[var_name] = 'point_coordinates'
                    elif 'Dot(' in assignment:
                        variables[var_name] = 'dot_object'
                    elif 'Text(' in assignment:
                        variables[var_name] = 'text_object'
                    elif 'axes.get_graph(' in assignment:
                        variables[var_name] = 'graph_object'
                    elif 'Line(' in assignment:
                        # Check if Line is properly constructed
                        if assignment.count('axes.c2p(') >= 2:
                            variables[var_name] = 'line_object'
                        else:
                            errors.append(f"Line {i}: Line object '{var_name}' needs two coordinate points: Line(axes.c2p(x1,y1), axes.c2p(x2,y2))")
                    elif 'Circle(' in assignment or 'Rectangle(' in assignment:
                        variables[var_name] = 'geometric_object'

            # Enhanced self.play() validation
            if 'self.play(' in line:
                # Check for direct coordinate usage in self.play
                if 'axes.c2p(' in line and 'Dot(' not in line:
                    errors.append(f"Line {i}: CRITICAL - Cannot use axes.c2p() directly in self.play(). Create Dot object first!")

                # Check for method calls without proper animation wrapping
                if '.scale(' in line or '.shift(' in line or '.rotate(' in line:
                    errors.append(f"Line {i}: WRONG - Cannot use .scale()/.shift()/.rotate() directly. Use ApplyMethod() or proper animations.")

                # Check for .set_color without animation wrapper
                if '.set_color(' in line:
                    errors.append(f"Line {i}: WRONG - Cannot use .set_color() directly. Use ApplyMethod() or proper color animation.")

            # Enhanced animation validation
            animation_functions = ['FadeOut', 'FadeIn', 'ShowCreation', 'Write', 'Transform']
            for anim_func in animation_functions:
                if f'{anim_func}(' in line:
                    # Extract variables being animated
                    anim_vars = re.findall(f'{anim_func}\\(([^,)]+)', line)
                    for var in anim_vars:
                        var = var.strip()
                        # Check if trying to animate coordinates
                        if 'axes.c2p(' in var:
                            errors.append(f"Line {i}: CRITICAL - Cannot animate coordinates with {anim_func}(). Create Dot object first!")
                        # Check if trying to animate coordinate variables
                        elif var in variables and variables[var] == 'point_coordinates':
                            errors.append(f"Line {i}: CRITICAL - Variable '{var}' contains coordinates. Create Dot object: dot = Dot({var})")

            # Check for missing indentation in construct method
            if in_construct and in_class and line and not line.startswith('def ') and not original_line.startswith('    '):
                if not line.startswith('#') and line != '}' and line != ')':
                    errors.append(f"Line {i}: INDENTATION ERROR - Code inside construct() must be indented with 4 spaces")

            # Check for common syntax errors
            if line.endswith(',') and not line.endswith(',)'):
                # Check if it's a trailing comma that might cause issues
                if 'self.play(' in line or 'Transform(' in line or 'ShowCreation(' in line:
                    errors.append(f"Line {i}: SYNTAX WARNING - Trailing comma might cause issues")

        # Final structure validation
        if not in_class:
            errors.append("STRUCTURE ERROR: No Scene class found")
        if not in_construct:
            errors.append("STRUCTURE ERROR: No construct() method found")

        return len(errors) == 0, "; ".join(errors)

    def _retry_with_backup_key(self, prompt: str, content_type: str,
                              original_prompt: str, duration: float = None) -> str:
        """Retry generation with backup API key"""
        global current_api_key, backup_key

        if backup_key and current_api_key != backup_key:
            try:
                print(f"🔄 Switching to backup API key for {content_type} generation...")

                # Switch to backup key
                current_api_key = backup_key
                genai.configure(api_key=current_api_key)

                # Recreate model with new key
                backup_model = genai.GenerativeModel("gemini-1.5-flash")

                # Retry generation
                response = backup_model.generate_content(prompt)
                result = response.text.strip()

                if content_type == "script":
                    result = self._clean_script(result)
                elif content_type == "video":
                    result = self._clean_manim_code(result)

                    # Validate video code with enhanced validation
                    is_valid, error_msg = self._validate_manim_code(result)
                    if not is_valid:
                        print(f"⚠️ Backup code has validation issues: {error_msg}")
                        print("🚨 CRITICAL: Even backup key generated invalid code")
                        # Don't use fallback - let the main retry loop handle this
                        return None

                print(f"✅ Successfully generated {content_type} with backup key!")
                return result

            except Exception as e:
                print(f"❌ Backup key also failed: {e}")

                # Switch back to primary key for future attempts
                current_api_key = primary_key
                genai.configure(api_key=current_api_key)

        # No fallback content - let main retry loop handle failures
        return None

    def _suggest_animation_for_text(self, text: str) -> str:
        """Suggest specific animations based on text content"""
        text_lower = text.lower()

        # Specific mathematical concepts with detailed animations
        if any(word in text_lower for word in ['quadratic', 'parabola', 'x squared', 'x²']):
            return "Show parabola formation, highlight vertex, demonstrate shape changes"
        elif any(word in text_lower for word in ['function', 'graph', 'plot', 'curve']):
            return "Animate function graph drawing, add labels, show key points"
        elif any(word in text_lower for word in ['equation', 'formula', 'equals']):
            return "Write equation step-by-step, highlight terms, show relationships"
        elif any(word in text_lower for word in ['parameter', 'coefficient', 'constant']):
            return "Highlight specific terms, show parameter effects with color changes"
        elif any(word in text_lower for word in ['vertex', 'maximum', 'minimum', 'peak']):
            return "Mark vertex point, draw vertex form, highlight extremum"
        elif any(word in text_lower for word in ['axis', 'coordinate', 'grid']):
            return "Create coordinate system, add labels, show scale"
        elif any(word in text_lower for word in ['transform', 'change', 'shift', 'move']):
            return "Animate transformation, show before/after, use arrows"
        elif any(word in text_lower for word in ['example', 'specific', 'case', 'instance']):
            return "Show concrete example, substitute values, demonstrate concept"
        elif any(word in text_lower for word in ['compare', 'different', 'various', 'multiple']):
            return "Show multiple examples side-by-side, use different colors"
        elif any(word in text_lower for word in ['derivative', 'slope', 'tangent', 'rate']):
            return "Draw tangent line, show slope calculation, animate rate of change"
        elif any(word in text_lower for word in ['integral', 'area', 'under', 'accumulation']):
            return "Fill area under curve, show Riemann sums, animate accumulation"
        elif any(word in text_lower for word in ['circle', 'radius', 'center']):
            return "Draw circle, mark center and radius, show construction"
        elif any(word in text_lower for word in ['triangle', 'angle', 'side']):
            return "Create triangle, highlight angles/sides, show measurements"
        elif any(word in text_lower for word in ['vector', 'direction', 'magnitude']):
            return "Draw vector arrow, show components, demonstrate operations"
        elif any(word in text_lower for word in ['sine', 'cosine', 'trigonometry']):
            return "Show unit circle, animate trig functions, mark key angles"
        elif any(word in text_lower for word in ['welcome', 'introduction', 'hello', 'today']):
            return "Show animated title, welcoming text effects, topic introduction"
        elif any(word in text_lower for word in ['conclusion', 'summary', 'remember', 'recap']):
            return "Highlight key points, show summary text, create memorable visual"
        else:
            return "Create dynamic visual element, use text highlights or geometric shapes"
    
    def _clean_script(self, script: str) -> str:
        """Clean and format the script"""
        # Remove markdown formatting
        script = re.sub(r'[*_#\[\]()]', '', script)
        script = re.sub(r'```.*?```', '', script, flags=re.DOTALL)
        
        # Clean up extra whitespace
        script = re.sub(r'\n\s*\n', '\n\n', script)
        script = script.strip()
        
        return script
    
    def _clean_manim_code(self, code: str) -> str:
        """Clean manim code by removing markdown blocks"""
        # Remove markdown code blocks
        code = re.sub(r'^```python\s*\n', '', code, flags=re.MULTILINE)
        code = re.sub(r'^```\s*$', '', code, flags=re.MULTILINE)
        code = re.sub(r'```.*?```', '', code, flags=re.DOTALL)
        
        # Remove any remaining backticks
        code = code.replace('`', '')
        
        # Clean up extra whitespace
        lines = code.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.rstrip()
            if line or (cleaned_lines and cleaned_lines[-1]):
                cleaned_lines.append(line)
        
        # Remove trailing empty lines
        while cleaned_lines and not cleaned_lines[-1]:
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
    
    # FALLBACK CODE METHODS COMMENTED OUT - WE NOW USE LLM ERROR FEEDBACK INSTEAD

    # def _generate_fallback_code(self, prompt: str, duration: float) -> str:
    #     """Generate sophisticated fallback code with rich animations"""
    #     class_name = "".join(word.capitalize() for word in prompt.split()[:3])
    #
    #     # Create a more sophisticated fallback based on topic
    #     if any(word in prompt.lower() for word in ['function', 'graph', 'equation']):
    #         return self._generate_function_fallback(class_name, prompt, duration)
    #     elif any(word in prompt.lower() for word in ['circle', 'trigonometry', 'sine', 'cosine']):
    #         return self._generate_circle_fallback(class_name, prompt, duration)
    #     elif any(word in prompt.lower() for word in ['triangle', 'geometry', 'angle']):
    #         return self._generate_geometry_fallback(class_name, prompt, duration)
    #     else:
    #         return self._generate_general_fallback(class_name, prompt, duration)

    def _generate_function_fallback(self, class_name: str, prompt: str, duration: float) -> str:
        """Generate function-based fallback with continuous animations"""
        return f"""from manimlib import *

class {class_name}(Scene):
    def construct(self):
        # Segment 1: Introduction (0-3s)
        title = Text("{prompt.title()}", font_size=48)
        self.play(Write(title), run_time=2)
        self.wait(1)

        # Segment 2: Setup coordinate system (3-6s)
        self.play(FadeOut(title), run_time=1)
        axes = Axes(x_range=[-3, 3], y_range=[-2, 4])
        axes.add_coordinate_labels()
        self.play(ShowCreation(axes), run_time=2)

        # Segment 3: Show function concept (6-10s)
        if "linear" in "{prompt.lower()}":
            func = axes.get_graph(lambda x: 2*x + 1, color=BLUE)
            func_label = axes.get_graph_label(func, "f(x) = 2x + 1")
        else:
            func = axes.get_graph(lambda x: x**2, color=BLUE)
            func_label = axes.get_graph_label(func, "f(x) = x²")

        self.play(ShowCreation(func), run_time=2)
        self.play(Write(func_label), run_time=2)

        # Segment 4: Highlight key features (10-{duration-6:.1f}s)
        if "linear" in "{prompt.lower()}":
            # Show slope
            slope_line = Line(axes.c2p(-1, -1), axes.c2p(1, 3), color=RED)
            slope_text = Text("Slope = 2", font_size=24).next_to(slope_line, UP)
            self.play(ShowCreation(slope_line), run_time=2)
            self.play(Write(slope_text), run_time=2)
            self.wait({duration-16:.1f})
        else:
            # Show vertex for quadratic
            vertex_dot = Dot(axes.c2p(0, 0), color=RED)
            vertex_text = Text("Vertex", font_size=24).next_to(vertex_dot, UP)
            self.play(ShowCreation(vertex_dot), run_time=1)
            self.play(Write(vertex_text), run_time=2)
            self.wait({duration-15:.1f})

        # Segment 5: Conclusion (last 3s)
        conclusion = Text("Mathematical Beauty", font_size=36)
        self.play(Write(conclusion), run_time=2)
        self.wait(1)
"""

    def _generate_circle_fallback(self, class_name: str, prompt: str, duration: float) -> str:
        """Generate circle-based fallback with continuous animations"""
        return f"""from manimlib import *

class {class_name}(Scene):
    def construct(self):
        # Segment 1: Introduction (0-3s)
        title = Text("{prompt.title()}", font_size=48)
        self.play(Write(title), run_time=2)
        self.wait(1)

        # Segment 2: Create circle (3-7s)
        self.play(FadeOut(title), run_time=1)
        circle = Circle(radius=2, color=BLUE)
        self.play(ShowCreation(circle), run_time=3)

        # Segment 3: Show center and radius (7-11s)
        center_dot = Dot(ORIGIN, color=RED)
        center_label = Text("Center", font_size=24).next_to(center_dot, DOWN)
        self.play(ShowCreation(center_dot), run_time=1)
        self.play(Write(center_label), run_time=2)
        self.wait(1)

        # Segment 4: Demonstrate radius (11-{duration-4:.1f}s)
        radius_line = Line(ORIGIN, circle.point_at_angle(0), color=YELLOW)
        radius_label = Text("Radius", font_size=24).next_to(radius_line, UP)
        self.play(ShowCreation(radius_line), run_time=2)
        self.play(Write(radius_label), run_time=2)

        # Animate radius rotation
        for angle in [PI/2, PI, 3*PI/2]:
            new_radius = Line(ORIGIN, circle.point_at_angle(angle), color=YELLOW)
            self.play(Transform(radius_line, new_radius), run_time=1)

        self.wait({duration-21:.1f})

        # Segment 5: Conclusion (last 3s)
        conclusion = Text("Circular Perfection", font_size=36)
        self.play(Write(conclusion), run_time=2)
        self.wait(1)
"""

    def _generate_geometry_fallback(self, class_name: str, prompt: str, duration: float) -> str:
        """Generate geometry-based fallback animation"""
        return f"""from manimlib import *

class {class_name}(Scene):
    def construct(self):
        # Title (0-3s)
        title = Text("{prompt.title()}", font_size=48)
        self.play(Write(title), run_time=2)
        self.wait(1)

        # Create triangle (3-{duration-5:.1f}s)
        self.play(FadeOut(title), run_time=1)
        triangle = Polygon([-2, -1, 0], [2, -1, 0], [0, 2, 0], color=BLUE)
        vertices = [Dot(point, color=RED) for point in triangle.get_vertices()]

        self.play(ShowCreation(triangle), run_time=2)
        for vertex in vertices:
            self.play(ShowCreation(vertex), run_time=0.5)
        self.wait({duration-9:.1f})

        # Conclusion (last 3s)
        conclusion = Text("Geometric Harmony", font_size=36)
        self.play(Write(conclusion), run_time=2)
        self.wait(1)
"""

    def _generate_general_fallback(self, class_name: str, prompt: str, duration: float) -> str:
        """Generate general mathematical fallback animation"""
        return f"""from manimlib import *

class {class_name}(Scene):
    def construct(self):
        # Title (0-3s)
        title = Text("{prompt.title()}", font_size=48)
        self.play(Write(title), run_time=2)
        self.wait(1)

        # Mathematical elements (3-{duration-5:.1f}s)
        self.play(FadeOut(title), run_time=1)

        # Create some mathematical text
        equation = Text("f(x) = ax² + bx + c", font_size=36)
        self.play(Write(equation), run_time=2)
        self.wait(2)

        # Transform to specific case
        specific = Text("f(x) = x²", font_size=36)
        self.play(Transform(equation, specific), run_time=2)
        self.wait({duration-10:.1f})

        # Conclusion (last 3s)
        conclusion = Text("Mathematics is Beautiful", font_size=36)
        self.play(Write(conclusion), run_time=2)
        self.wait(1)
"""
    
    def _extract_search_terms(self, prompt: str) -> str:
        """Extract key terms for RAG search"""
        math_terms = ["derivative", "integral", "function", "graph", "vector", "matrix", 
                     "equation", "theorem", "proof", "limit", "series", "transform",
                     "exponential", "logarithm", "trigonometry", "geometry", "algebra"]
        
        terms = []
        prompt_lower = prompt.lower()
        for term in math_terms:
            if term in prompt_lower:
                terms.append(term)
        
        terms.append(prompt)
        return " ".join(terms)

    def _get_perfect_examples(self) -> str:
        """Get perfect syntax examples for common patterns"""
        return """
# PERFECT COORDINATE USAGE PATTERNS:
# ✅ CORRECT: Create objects first, then animate
point = Dot(axes.c2p(2, 3), color=RED)
self.play(ShowCreation(point), run_time=2)

line = Line(axes.c2p(0, 0), axes.c2p(2, 3), color=BLUE)
self.play(ShowCreation(line), run_time=2)

# ✅ CORRECT: Graph creation and animation
graph = axes.get_graph(lambda x: x**2, color=GREEN)
self.play(ShowCreation(graph), run_time=3)

# ✅ CORRECT: Transform between objects
graph1 = axes.get_graph(lambda x: x**2, color=BLUE)
graph2 = axes.get_graph(lambda x: 2*x**2, color=GREEN)
self.play(ShowCreation(graph1), run_time=2)
self.play(Transform(graph1, graph2), run_time=3)

# ✅ CORRECT: Text with fade
text = Text("Explanation").to_edge(UP)
self.play(Write(text), run_time=2)
self.play(FadeOut(text), run_time=0.5)

# ❌ NEVER DO THESE:
# point = axes.c2p(1, 2)  # This assigns coordinates, not objects!
# self.play(FadeOut(axes.c2p(1, 2)))  # Cannot animate coordinates!
# Transform(new_graph)  # Missing source object!
"""

    def _generate_rich_manim_animations(self, prompt: str, script: str,
                                      segments: List[Dict], total_duration: float) -> str:
        """Generate rich manim animation code with enhanced prompts"""

        # Create timing breakdown
        timing_breakdown = ""
        for i, segment in enumerate(segments, 1):
            timing_breakdown += f"Segment {i}: \"{segment.get('text', '')[:50]}...\" ({segment.get('duration', 0):.1f}s)\n"

        # Get relevant examples from RAG
        relevant_examples = ""
        if self.rag:
            try:
                search_terms = self._extract_search_terms(prompt)
                relevant_examples = self.rag.get_relevant_examples(search_terms)
            except Exception as e:
                print(f"Error getting RAG examples: {e}")

        # Add perfect synthetic examples for common patterns
        perfect_examples = self._get_perfect_examples()

        examples_section = ""
        if relevant_examples or perfect_examples:
            examples_section = f"""
RELEVANT 3BLUE1BROWN EXAMPLES:
{relevant_examples}

PERFECT SYNTAX EXAMPLES (MANDATORY PATTERNS):
{perfect_examples}
"""

        # Enhanced prompt specifically for rich manim animations
        video_prompt = f"""
🚨 RICH MANIM ANIMATION CODE GENERATION

You MUST create RICH MATHEMATICAL ANIMATIONS using full manim capabilities.

SCRIPT CONTENT:
{script}

TIMING BREAKDOWN:
{timing_breakdown}

🎯 CRITICAL REQUIREMENTS FOR RICH ANIMATIONS:
1. 🎯 USE 3BLUE1BROWN MANIM: from manimlib import * (NOT from manim import *)
2. 🎯 EFFICIENT ANIMATIONS: Keep text short, focus on mathematical visuals
3. 🎯 SCRIPT SYNCHRONIZATION: Each animation must match what's being said
4. 🎯 RICH VISUAL ELEMENTS: Graphs, transformations, moving objects
5. 🎯 ADVANCED MANIM FEATURES: ShowCreation, Transform, FadeIn/Out, Write
6. 🎯 FAST RENDERING: Avoid overly long text blocks, keep animations concise

{examples_section}

🎨 RICH MATHEMATICAL ANIMATION REQUIREMENTS:

FOR MATHEMATICAL CONCEPTS:
- Create detailed coordinate systems with labeled axes
- Show function graphs being drawn step by step
- Demonstrate transformations with smooth animations
- Use moving points to trace curves
- Show mathematical relationships visually
- Create geometric constructions
- Animate equation building and solving

FOR SCRIPT SYNCHRONIZATION:
- If script mentions "parabola": Create detailed parabola with vertex
- If script mentions "function": Show function being graphed
- If script mentions "transformation": Show actual transformation animations
- If script mentions "point": Show points with coordinates and labels
- If script mentions "equation": Build equation step by step

MANDATORY 3BLUE1BROWN MANIM PATTERNS:
```python
# CORRECT 3B1B MANIM SYNTAX:
from manimlib import *  # NOT from manim import *

# Efficient parabola visualization
axes = Axes(x_range=(-5, 5), y_range=(-5, 5))
parabola = axes.get_graph(lambda x: x**2, color=BLUE)
self.play(ShowCreation(axes), run_time=2)
self.play(ShowCreation(parabola), run_time=2)

# Short text (max 30 characters)
text = Text("Parabola").to_edge(UP)
self.play(Write(text), run_time=1)
self.play(FadeOut(text), run_time=0.5)

# Function transformation
new_func = axes.get_graph(lambda x: 2*x**2, color=GREEN)
self.play(Transform(parabola, new_func), run_time=2)

# FORBIDDEN PATTERNS (cause errors):
# ❌ NO: obj.add_updater(lambda m: m.set_value(...))
# ❌ NO: self.play(FadeOut(obj_with_updater))
# ❌ NO: Complex lambda functions in animations
# ❌ NO: ApplyMethod with about_point parameter
# ❌ NO: about_point in any animation
# ❌ NO: TangentLine (causes array ambiguity errors)
# ❌ NO: Complex geometry objects with multiple parameters
# ✅ YES: Use Transform, ShowCreation, Write, FadeIn, FadeOut only
# ✅ YES: Use simple objects: Line, Dot, Text, MathTex, axes.get_graph
```

🚨 CRITICAL EFFICIENCY RULES:
- Keep text under 30 characters per Text() object
- Use run_time between 0.5-3 seconds maximum
- Focus on mathematical objects, not long explanations
- Use 3Blue1Brown manim syntax (manimlib)
- AVOID complex updaters and lambda functions
- AVOID FadeOut on objects with updaters
- Use simple, static objects for reliable rendering

🚨 OUTPUT FORMAT REQUIREMENTS:
- Generate ONLY Python code - NO explanations, comments, or instructions
- NO installation instructions or setup notes
- NO markdown formatting or code blocks
- Start with "from manimlib import *" and end with the last line of code
- PURE PYTHON CODE ONLY

🎯 GENERATE COMPLETE MANIM SCENE CLASS:
- Class name: VideoScene
- Rich mathematical animations for EVERY script segment
- Perfect timing synchronization
- Advanced manim features throughout
- Educational mathematical demonstrations

TOPIC: {prompt}
TOTAL DURATION: {total_duration:.1f} seconds

Generate the complete manim code with rich mathematical animations:
"""

        try:
            response = self.model.generate_content(video_prompt)
            # Clean the response to remove markdown formatting and explanations
            code = response.text.strip()

            # Remove markdown code blocks
            if code.startswith('```python'):
                code = code[9:]  # Remove ```python
            if code.startswith('```'):
                code = code[3:]   # Remove ```
            if code.endswith('```'):
                code = code[:-3]  # Remove trailing ```

            # Aggressive cleaning to remove all non-Python content
            lines = code.split('\n')
            python_lines = []
            in_python_code = False

            for line in lines:
                # Start collecting when we see the import
                if line.strip().startswith('from manimlib import'):
                    in_python_code = True

                if in_python_code:
                    # Stop if we hit explanatory text patterns
                    if (line.strip().startswith(('1.', '2.', '3.', 'Note:', 'This code',
                                               'The animation', 'Explanation:', 'Install',
                                               'Make sure', 'Remember', '**', 'To run'))) or \
                       ('install' in line.lower() and 'pip' in line.lower()):
                        break
                    python_lines.append(line)

            code = '\n'.join(python_lines).strip()
            return code
        except Exception as e:
            print(f"Error generating rich manim code: {e}")
            return self._create_fallback_rich_code(segments, prompt)

    def _create_fallback_rich_code(self, segments: List[Dict], topic: str) -> str:
        """Create rich fallback code if generation fails"""

        code = """from manimlib import *

class VideoScene(Scene):
    def construct(self):
        # Setup rich coordinate system
        axes = Axes(
            x_range=(-5, 5, 1),
            y_range=(-5, 5, 1),
            axis_config={"color": WHITE}
        )
        axes_labels = axes.get_axis_labels(x_label="x", y_label="y")
        self.play(ShowCreation(axes), Write(axes_labels), run_time=3)

"""

        for i, segment in enumerate(segments, 1):
            duration = segment.get('duration', 2.0)
            text = segment.get('text', f'Segment {i}')[:30]
            clean_text = text.replace('"', "'").replace('\n', ' ').strip()

            code += f"""        # Segment {i}: Rich mathematical animation
        text{i} = Text("{clean_text}").to_edge(UP)
        self.play(Write(text{i}), run_time={min(duration * 0.4, 2.0):.1f})

        # Rich mathematical visualization
        func{i} = axes.get_graph(lambda x: x**2 + {i-3}, color=BLUE)
        point{i} = Dot(axes.c2p({i-3}, {(i-3)**2}), color=RED)
        self.play(ShowCreation(func{i}), run_time={max(duration * 0.4, 2.0):.1f})
        self.play(ShowCreation(point{i}), run_time=1)
        self.play(FadeOut(text{i}), run_time=0.5)

"""

        return code

    def _save_audio_first_content(self, result: Dict, job_id: str):
        """Save all generated content"""
        base_path = Path(f"generated/{job_id}")
        base_path.mkdir(parents=True, exist_ok=True)
        
        # Save script
        with open(base_path / "script.txt", "w", encoding="utf-8") as f:
            f.write(result["script"])
        
        # Save manim code
        with open(base_path / "manim_code.py", "w", encoding="utf-8") as f:
            f.write(result["manim_code"])
        
        # Save generation info
        generation_info = {
            "method": "audio_first",
            "natural_duration": result["natural_duration"],
            "audio_timing": result["audio_timing"],
            "audio_path": result["audio_path"]
        }
        
        with open(base_path / "generation_info.json", "w", encoding="utf-8") as f:
            json.dump(generation_info, f, indent=2)
        
        print(f"📁 Audio-first content saved to: {base_path}")


def generate_audio_first_video(prompt: str, job_id: str) -> Dict:
    """Main function to generate audio-first synchronized video"""
    generator = AudioFirstGenerator()
    return generator.generate_audio_first_content(prompt, job_id)


if __name__ == "__main__":
    # Test the audio-first generator
    test_prompt = "quadratic functions and their graphs"
    test_job_id = "audio_first_test"
    
    try:
        result = generate_audio_first_video(test_prompt, test_job_id)
        print("✅ Audio-first generation test successful!")
        print(f"📊 Natural duration: {result['natural_duration']:.2f}s")
        print(f"🎵 Audio: {result['audio_path']}")
    except Exception as e:
        print(f"❌ Test failed: {e}")
