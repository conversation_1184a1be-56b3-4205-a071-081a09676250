#!/usr/bin/env python3
"""
Optimized Audio-First 3Blue1Brown Video Generator
Enhanced quality control and error prevention system
"""

import os
import sys
import json
import time
from pathlib import Path
from dotenv import load_dotenv

# Import our enhanced audio-first modules
from app.audio_first_generator import AudioFirstGenerator
from app.manim_3b1b_runner import generate_video_3b1b, check_3b1b_manim_installation
from app.video_merger import merge_audio_video

load_dotenv()


class OptimizedAudioFirstGenerator:
    """Enhanced audio-first generator with quality control and metrics"""
    
    def __init__(self):
        self.generator = AudioFirstGenerator()
        self.quality_metrics = {
            "total_attempts": 0,
            "validation_failures": 0,
            "syntax_errors": 0,
            "coordinate_errors": 0,
            "success_rate": 0.0,
            "generation_time": 0.0
        }
    
    def generate_optimized_video(self, topic: str, job_id: str = None, 
                               max_retries: int = 3) -> dict:
        """
        Generate high-quality video with enhanced error prevention
        
        Args:
            topic: Educational topic to visualize
            job_id: Unique identifier (auto-generated if None)
            max_retries: Maximum retry attempts for failed generations
            
        Returns:
            Dictionary with results, files, and quality metrics
        """
        
        start_time = time.time()
        
        # Generate job ID if not provided
        if job_id is None:
            import uuid
            job_id = f"opt_{str(uuid.uuid4())[:8]}"
        
        print("🎯 OPTIMIZED AUDIO-FIRST GENERATION")
        print("=" * 60)
        print(f"📋 Topic: {topic}")
        print(f"🆔 Job ID: {job_id}")
        print(f"🔄 Max Retries: {max_retries}")
        print()
        
        results = {
            "success": False,
            "job_id": job_id,
            "topic": topic,
            "files": {},
            "errors": [],
            "quality_metrics": {},
            "generation_time": 0.0
        }
        
        # Retry loop for enhanced reliability
        for attempt in range(max_retries + 1):
            try:
                print(f"🚀 Generation Attempt {attempt + 1}/{max_retries + 1}")
                print("-" * 40)
                
                # Step 1: Generate audio-first content with enhanced validation
                print("🎵 Step 1: Audio-First Content Generation...")
                content_result = self.generator.generate_audio_first_content(topic, job_id)
                
                if not content_result:
                    raise Exception("Audio-first content generation failed")
                
                print(f"✅ Audio-first content generated successfully")
                print(f"🎵 Audio Duration: {content_result.get('natural_duration', 0):.1f}s")
                print(f"📊 Audio Segments: {len(content_result.get('segments', []))}")
                print()
                
                # Step 2: Enhanced video generation with quality checks
                print("🎬 Step 2: Enhanced Video Generation...")
                
                # Read and validate the generated code
                code_path = content_result.get('code_path', f"generated/{job_id}/manim_code.py")
                if not os.path.exists(code_path):
                    raise Exception(f"Generated code file not found: {code_path}")
                
                with open(code_path, 'r', encoding='utf-8') as f:
                    manim_code = f.read()
                
                # Pre-generation code quality check
                print("🔍 Pre-generation code quality check...")
                quality_score = self._assess_code_quality(manim_code)
                print(f"📊 Code Quality Score: {quality_score:.1f}/10")
                
                if quality_score < 7.0:
                    print("⚠️ Code quality below threshold, but proceeding...")
                
                # Generate video with 3Blue1Brown manim
                video_path = generate_video_3b1b(manim_code, job_id, output_format="mp4")
                
                if not video_path or not os.path.exists(video_path):
                    raise Exception("Video generation failed - no output file")
                
                print(f"✅ Video generated successfully: {video_path}")
                print()
                
                # Step 3: Audio-video integration
                print("🔗 Step 3: Audio-Video Integration...")
                
                audio_path = content_result.get('audio_path')
                if audio_path and os.path.exists(audio_path):
                    try:
                        final_video_path = merge_audio_video(
                            audio_path, 
                            video_path, 
                            job_id,
                            output_name="optimized_complete_video"
                        )
                        
                        if final_video_path and os.path.exists(final_video_path):
                            print(f"✅ Integrated video created: {final_video_path}")
                            results["files"]["final_video"] = final_video_path
                        else:
                            print("⚠️ Audio-video merge failed, using video-only")
                            results["files"]["final_video"] = video_path
                    except Exception as merge_error:
                        print(f"⚠️ Audio-video merge failed: {merge_error}")
                        print("📹 Using video-only output")
                        results["files"]["final_video"] = video_path
                else:
                    print("📹 Using video-only output (no audio file)")
                    results["files"]["final_video"] = video_path
                
                # Success! Populate results
                results["success"] = True
                results["files"].update({
                    "video": video_path,
                    "audio": content_result.get('audio_path', ''),
                    "script": content_result.get('script_path', f"generated/{job_id}/script.txt"),
                    "code": code_path
                })
                
                # Calculate quality metrics
                end_time = time.time()
                results["generation_time"] = end_time - start_time
                results["quality_metrics"] = self._calculate_quality_metrics(
                    attempt + 1, quality_score, results["generation_time"]
                )
                
                print()
                print("🎉 OPTIMIZED GENERATION COMPLETED SUCCESSFULLY!")
                print(f"⏱️ Total Time: {results['generation_time']:.1f} seconds")
                print(f"🎯 Attempts Used: {attempt + 1}/{max_retries + 1}")
                print(f"📊 Quality Score: {quality_score:.1f}/10")
                
                return results
                
            except Exception as e:
                error_msg = str(e)
                print(f"❌ Attempt {attempt + 1} failed: {error_msg}")
                results["errors"].append(f"Attempt {attempt + 1}: {error_msg}")
                
                # Analyze error type for metrics
                self._analyze_error(error_msg)
                
                if attempt < max_retries:
                    print(f"🔄 Retrying... ({max_retries - attempt} attempts remaining)")
                    print()
                    time.sleep(2)  # Brief pause before retry
                else:
                    print("❌ All attempts exhausted")
                    break
        
        # If we get here, all attempts failed
        end_time = time.time()
        results["generation_time"] = end_time - start_time
        results["quality_metrics"] = self._calculate_quality_metrics(
            max_retries + 1, 0.0, results["generation_time"]
        )
        
        print()
        print("💥 GENERATION FAILED AFTER ALL ATTEMPTS")
        print(f"⏱️ Total Time: {results['generation_time']:.1f} seconds")
        print("🔍 Error Summary:")
        for error in results["errors"]:
            print(f"   • {error}")
        
        return results
    
    def _assess_code_quality(self, code: str) -> float:
        """Assess the quality of generated manim code"""
        score = 10.0
        
        # Check for common issues
        if "axes.c2p(" in code and "Dot(" not in code:
            score -= 3.0  # Major issue
        
        if "Transform(" in code:
            # Check if Transform has proper arguments
            import re
            single_arg_transforms = re.findall(r'Transform\s*\(\s*[^,)]+\s*\)', code)
            if single_arg_transforms:
                score -= 2.0
        
        if any(forbidden in code for forbidden in ["Tex(", "MathTex(", "Create("]):
            score -= 1.5
        
        if code.count("(") != code.count(")"):
            score -= 2.0  # Syntax issue
        
        # Positive indicators
        if "from manimlib import *" in code:
            score += 0.5
        
        if "def construct(self):" in code:
            score += 0.5
        
        if "self.play(" in code:
            score += 0.5
        
        return max(0.0, min(10.0, score))
    
    def _analyze_error(self, error_msg: str):
        """Analyze error types for metrics"""
        self.quality_metrics["total_attempts"] += 1
        
        if "validation" in error_msg.lower():
            self.quality_metrics["validation_failures"] += 1
        
        if "syntax" in error_msg.lower():
            self.quality_metrics["syntax_errors"] += 1
        
        if "coordinate" in error_msg.lower() or "axes.c2p" in error_msg:
            self.quality_metrics["coordinate_errors"] += 1
    
    def _calculate_quality_metrics(self, attempts: int, quality_score: float, 
                                 generation_time: float) -> dict:
        """Calculate comprehensive quality metrics"""
        return {
            "attempts_used": attempts,
            "code_quality_score": quality_score,
            "generation_time_seconds": generation_time,
            "success_rate": 1.0 if attempts <= 3 else 0.5,
            "efficiency_score": max(0.0, 10.0 - attempts - (generation_time / 30)),
            "total_validation_failures": self.quality_metrics["validation_failures"],
            "total_syntax_errors": self.quality_metrics["syntax_errors"],
            "total_coordinate_errors": self.quality_metrics["coordinate_errors"]
        }


def main():
    """Main function for optimized audio-first generation"""
    
    print("🎵 OPTIMIZED AUDIO-FIRST 3BLUE1BROWN VIDEO GENERATOR")
    print("=" * 70)
    print("🚀 Enhanced quality control and error prevention")
    print()
    
    # Environment validation
    if not os.getenv("GOOGLE_API_KEY") and not os.getenv("GOOGLE_API_KEY2"):
        print("❌ No Google API keys found in environment")
        print("💡 Please set GOOGLE_API_KEY or GOOGLE_API_KEY2")
        sys.exit(1)
    
    # Check 3Blue1Brown manim
    if not check_3b1b_manim_installation():
        print("❌ 3Blue1Brown manim not found")
        print("💡 Please run setup_3b1b_rag.py first")
        sys.exit(1)
    
    print("✅ Environment validation passed")
    print()
    
    # Get user input
    topic = input("Enter topic for optimized video generation: ").strip()
    if not topic:
        topic = "linear functions and slope"
        print(f"Using default topic: {topic}")
    
    max_retries = input("Max retry attempts (default: 3): ").strip()
    try:
        max_retries = int(max_retries) if max_retries else 3
    except ValueError:
        max_retries = 3
    
    print()
    
    # Generate optimized video
    generator = OptimizedAudioFirstGenerator()
    results = generator.generate_optimized_video(topic, max_retries=max_retries)
    
    # Display results
    print()
    print("📊 GENERATION SUMMARY")
    print("=" * 40)
    
    if results["success"]:
        print("✅ Status: SUCCESS")
        print(f"📁 Generated Files:")
        for file_type, path in results["files"].items():
            if path and os.path.exists(path):
                size_mb = os.path.getsize(path) / (1024 * 1024)
                print(f"   • {file_type}: {path} ({size_mb:.1f} MB)")
        
        metrics = results["quality_metrics"]
        print(f"📊 Quality Metrics:")
        print(f"   • Attempts Used: {metrics['attempts_used']}")
        print(f"   • Code Quality: {metrics['code_quality_score']:.1f}/10")
        print(f"   • Generation Time: {metrics['generation_time_seconds']:.1f}s")
        print(f"   • Efficiency Score: {metrics['efficiency_score']:.1f}/10")
        
    else:
        print("❌ Status: FAILED")
        print(f"🔍 Errors: {len(results['errors'])}")
        for error in results["errors"]:
            print(f"   • {error}")
    
    print()
    print("🎯 Next steps:")
    if results["success"]:
        print("1. Review the generated video quality")
        print("2. Check the quality metrics for optimization opportunities")
        print("3. Test with different topics to validate consistency")
    else:
        print("1. Review the error messages above")
        print("2. Check API quota and environment setup")
        print("3. Try with a simpler topic")


if __name__ == "__main__":
    main()
