"""
RAG (Retrieval-Augmented Generation) System for 3Blue1Brown Manim
This system indexes and retrieves relevant manim code examples to enhance LLM responses
"""

import os
import re
import ast
from pathlib import Path
from typing import List, Dict, Any, Optional
import json

try:
    import chromadb
    from sentence_transformers import SentenceTransformer
    from langchain.text_splitter import RecursiveCharacterTextSplitter
    from langchain.docstore.document import Document
except ImportError as e:
    print(f"Missing dependencies: {e}")
    print("Run: pip install chromadb sentence-transformers langchain")
    exit(1)


class ManimCodeExtractor:
    """Extract and process manim code from 3b1b repositories"""
    
    def __init__(self, base_path: str = "3b1b_data"):
        self.base_path = Path(base_path)
        self.manim_path = self.base_path / "manim"
        self.videos_path = self.base_path / "videos"
    
    def extract_python_files(self) -> List[Dict[str, Any]]:
        """Extract all Python files from 3b1b repositories"""
        files = []
        
        # Extract from manim library
        if self.manim_path.exists():
            for py_file in self.manim_path.rglob("*.py"):
                if self._is_valid_file(py_file):
                    files.append(self._process_file(py_file, "manim_library"))
        
        # Extract from videos repository
        if self.videos_path.exists():
            for py_file in self.videos_path.rglob("*.py"):
                if self._is_valid_file(py_file):
                    files.append(self._process_file(py_file, "video_examples"))
        
        return [f for f in files if f is not None]
    
    def _is_valid_file(self, file_path: Path) -> bool:
        """Check if file is valid for processing"""
        # Skip test files, __pycache__, etc.
        skip_patterns = [
            "__pycache__", ".git", "test", "tests", 
            ".pyc", "setup.py", "__init__.py"
        ]
        
        file_str = str(file_path)
        return not any(pattern in file_str for pattern in skip_patterns)
    
    def _process_file(self, file_path: Path, source_type: str) -> Optional[Dict[str, Any]]:
        """Process a single Python file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract classes and functions
            classes = self._extract_classes(content)
            functions = self._extract_functions(content)
            
            return {
                "file_path": str(file_path),
                "source_type": source_type,
                "content": content,
                "classes": classes,
                "functions": functions,
                "relative_path": str(file_path.relative_to(self.base_path))
            }
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return None
    
    def _extract_classes(self, content: str) -> List[Dict[str, str]]:
        """Extract class definitions from Python code"""
        classes = []
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_code = ast.get_source_segment(content, node)
                    if class_code:
                        classes.append({
                            "name": node.name,
                            "code": class_code,
                            "docstring": ast.get_docstring(node) or ""
                        })
        except:
            pass
        return classes
    
    def _extract_functions(self, content: str) -> List[Dict[str, str]]:
        """Extract function definitions from Python code"""
        functions = []
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    func_code = ast.get_source_segment(content, node)
                    if func_code:
                        functions.append({
                            "name": node.name,
                            "code": func_code,
                            "docstring": ast.get_docstring(node) or ""
                        })
        except:
            pass
        return functions


class ManimRAG:
    """RAG system for 3Blue1Brown manim code"""
    
    def __init__(self, db_path: str = "manim_rag_db"):
        self.db_path = db_path
        self.client = chromadb.PersistentClient(path=db_path)
        self.collection_name = "manim_code"
        self.encoder = SentenceTransformer('all-MiniLM-L6-v2')
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=200,
            separators=["\n\nclass ", "\n\ndef ", "\n\n", "\n", " "]
        )
        
    def setup(self):
        """Setup the RAG system"""
        print("🔧 Setting up Manim RAG system...")
        
        # Create or get collection
        try:
            self.collection = self.client.get_collection(self.collection_name)
            print("📚 Found existing collection")
        except:
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "3Blue1Brown manim code examples"}
            )
            print("📚 Created new collection")
        
        # Check if we need to populate
        if self.collection.count() == 0:
            self._populate_database()
        else:
            print(f"📊 Collection has {self.collection.count()} documents")
    
    def _populate_database(self):
        """Populate the vector database with manim code"""
        print("📥 Populating database with manim code...")
        
        extractor = ManimCodeExtractor()
        files = extractor.extract_python_files()
        
        documents = []
        metadatas = []
        ids = []
        
        doc_id = 0
        
        for file_data in files:
            # Add full file content
            chunks = self.text_splitter.split_text(file_data["content"])
            
            for i, chunk in enumerate(chunks):
                documents.append(chunk)
                metadatas.append({
                    "file_path": file_data["file_path"],
                    "source_type": file_data["source_type"],
                    "relative_path": file_data["relative_path"],
                    "chunk_id": i,
                    "content_type": "file_chunk"
                })
                ids.append(f"file_{doc_id}_{i}")
            
            # Add individual classes
            for cls_idx, cls in enumerate(file_data["classes"]):
                documents.append(f"Class: {cls['name']}\n{cls['code']}")
                metadatas.append({
                    "file_path": file_data["file_path"],
                    "source_type": file_data["source_type"],
                    "relative_path": file_data["relative_path"],
                    "content_type": "class",
                    "name": cls["name"],
                    "docstring": cls["docstring"]
                })
                ids.append(f"class_{doc_id}_{cls_idx}_{cls['name']}")

            # Add individual functions
            for func_idx, func in enumerate(file_data["functions"]):
                documents.append(f"Function: {func['name']}\n{func['code']}")
                metadatas.append({
                    "file_path": file_data["file_path"],
                    "source_type": file_data["source_type"],
                    "relative_path": file_data["relative_path"],
                    "content_type": "function",
                    "name": func["name"],
                    "docstring": func["docstring"]
                })
                ids.append(f"func_{doc_id}_{func_idx}_{func['name']}")
            
            doc_id += 1
        
        # Add to collection in batches
        batch_size = 100
        for i in range(0, len(documents), batch_size):
            batch_docs = documents[i:i+batch_size]
            batch_metas = metadatas[i:i+batch_size]
            batch_ids = ids[i:i+batch_size]
            
            # Generate embeddings
            embeddings = self.encoder.encode(batch_docs).tolist()
            
            self.collection.add(
                documents=batch_docs,
                metadatas=batch_metas,
                embeddings=embeddings,
                ids=batch_ids
            )
            
            print(f"📊 Added batch {i//batch_size + 1}/{(len(documents)-1)//batch_size + 1}")
        
        print(f"✅ Populated database with {len(documents)} documents")
    
    def search(self, query: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """Search for relevant manim code examples"""
        results = self.collection.query(
            query_texts=[query],
            n_results=n_results
        )
        
        formatted_results = []
        for i in range(len(results['documents'][0])):
            formatted_results.append({
                "content": results['documents'][0][i],
                "metadata": results['metadatas'][0][i],
                "distance": results['distances'][0][i] if 'distances' in results else None
            })
        
        return formatted_results
    
    def _filter_good_examples(self, results: list) -> list:
        """Filter out examples with problematic patterns"""
        good_results = []

        for result in results:
            content = result["content"]

            # Skip examples with known problematic patterns
            skip_patterns = [
                "FunctionGraph(",  # Use axes.get_graph instead
                "x_range=(",       # Old syntax, should use x_min, x_max
                "CONFIG =",        # Old config style
                "from manim import",  # Wrong import for 3b1b
                "from manimlib.imports import",  # Old import style
            ]

            # Check if content has problematic patterns
            has_problems = any(pattern in content for pattern in skip_patterns)

            # Only include if it has good 3b1b patterns
            good_patterns = [
                "from manimlib import *",
                "axes.get_graph(",
                "ShowCreation(",
                "Write(",
                "self.play(",
                "self.wait(",
            ]

            has_good_patterns = any(pattern in content for pattern in good_patterns)

            # Include if it has good patterns and no problematic ones
            if has_good_patterns and not has_problems:
                good_results.append(result)

        return good_results

    def get_relevant_examples(self, topic: str, animation_type: str = None) -> str:
        """Get relevant manim examples for a specific topic"""
        # Construct search query
        query_parts = [topic]
        if animation_type:
            query_parts.append(animation_type)

        query = " ".join(query_parts)

        # Search for relevant examples (get more to filter)
        results = self.search(query, n_results=6)

        # Filter for good examples
        good_results = self._filter_good_examples(results)

        # Take top 3 good examples
        good_results = good_results[:3]

        # Format results for LLM
        examples = []
        for result in good_results:
            content = result["content"]
            metadata = result["metadata"]

            example = f"""
# Example from {metadata.get('relative_path', 'unknown')}
# Type: {metadata.get('content_type', 'unknown')} - VERIFIED GOOD SYNTAX
{content}
"""
            examples.append(example)

        return "\n".join(examples)


def main():
    """Main function to setup and test RAG system"""
    rag = ManimRAG()
    rag.setup()
    
    # Test search
    print("\n🔍 Testing search...")
    results = rag.search("vector animation transform", n_results=3)
    
    for i, result in enumerate(results):
        print(f"\n--- Result {i+1} ---")
        print(f"Type: {result['metadata'].get('content_type')}")
        print(f"File: {result['metadata'].get('relative_path')}")
        print(f"Content preview: {result['content'][:200]}...")


if __name__ == "__main__":
    main()
