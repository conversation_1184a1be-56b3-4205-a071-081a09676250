"""
Synchronized Audio-Video Generation System
Generates script and manim code with precise timing coordination
"""

import os
import json
import re
from pathlib import Path
from typing import Dict, List, Tuple
import google.generativeai as genai
from dotenv import load_dotenv

# Import existing modules
from .rag_system import ManimRAG
from .tts import text_to_speech, extract_sentences
from .manim_3b1b_runner import generate_video_3b1b

load_dotenv()

# Try primary API key first, fallback to secondary if quota exceeded
primary_key = os.getenv("GOOGLE_API_KEY2")
backup_key = os.getenv("GOOGLE_API_KEY")
current_api_key = primary_key

genai.configure(api_key=current_api_key)


class SynchronizedContentGenerator:
    """Generates synchronized script and manim code with precise timing"""
    
    def __init__(self):
        self.model = genai.GenerativeModel("gemini-1.5-flash")
        self.rag = None
        
        # Initialize RAG if available
        if ManimRAG:
            try:
                self.rag = ManimRAG()
                self.rag.setup()
                print("✅ RAG system initialized for synchronized generation")
            except Exception as e:
                print(f"⚠️ RAG system failed to initialize: {e}")
                self.rag = None
    
    def estimate_speech_duration(self, text: str, words_per_minute: int = 150) -> float:
        """Estimate speech duration based on word count and speaking rate"""
        # Extract sentences that will actually be spoken
        sentences = extract_sentences(text)
        total_text = " ".join(sentences)
        
        # Count words
        words = len(total_text.split())
        
        # Calculate duration (including pauses between sentences)
        base_duration = (words / words_per_minute) * 60
        pause_duration = len(sentences) * 0.5  # 500ms pause between sentences
        
        return base_duration + pause_duration
    
    def generate_synchronized_content(self, prompt: str, job_id: str, 
                                    target_duration: float = None) -> Dict:
        """Generate script and manim code with synchronized timing"""
        
        # Get relevant examples from RAG
        relevant_examples = ""
        if self.rag:
            try:
                search_terms = self._extract_search_terms(prompt)
                relevant_examples = self.rag.get_relevant_examples(search_terms)
            except Exception as e:
                print(f"Error getting RAG examples: {e}")
        
        # Build enhanced prompt with timing requirements
        examples_section = ""
        if relevant_examples:
            examples_section = f"RELEVANT EXAMPLES FROM 3BLUE1BROWN CODEBASE:\n{relevant_examples}\n"
        
        # Determine target duration
        if target_duration is None:
            target_duration = 30.0  # Default 30 seconds
        
        user_prompt = f"""
You are a professional Manim animation expert using 3Blue1Brown's original manim library. 
You MUST generate synchronized script and animation code with precise timing.

CRITICAL TIMING REQUIREMENTS:
- Target video duration: {target_duration} seconds
- Script must be exactly the right length for this duration
- Animation timing must match narration pace
- Use self.wait() calls strategically to sync with speech

SCRIPT STRUCTURE REQUIREMENTS:
1. Write script in clear, timed segments
2. Each segment should correspond to a specific animation
3. Use natural speaking pace (150 words per minute)
4. Include timing cues in comments

ANIMATION TIMING REQUIREMENTS:
1. Match animation duration to estimated speech time
2. Use appropriate self.wait() durations
3. Sync visual reveals with narration points
4. Include pauses for comprehension

CRITICAL SYNTAX RULES FOR 3BLUE1BROWN MANIM:
1. IMPORTS: from manimlib import *
2. SCENE: class YourScene(Scene):
3. TIMING: Use self.wait(duration) for precise timing
4. ANIMATIONS: Use self.play() with run_time parameter
5. COORDINATES: Always use numpy arrays like np.array([x, y, z])
6. CREATION: Use ShowCreation() NOT Create()
7. WRITING: Use Write() for text
8. TRANSFORMS: Use Transform() or ReplacementTransform()
9. REMOVAL: Use FadeOut() or Uncreate()
10. GRAPHS: Use axes.get_graph() for function plotting

{examples_section}

TASK: Generate synchronized educational content for: {prompt}

OUTPUT FORMAT (JSON):
{{
  "script": "Educational narration script with timing cues...",
  "manim_code": "Synchronized 3b1b manim code with precise timing...",
  "estimated_duration": {target_duration},
  "timing_segments": [
    {{"text": "First segment text", "start_time": 0, "duration": 5}},
    {{"text": "Second segment text", "start_time": 5, "duration": 8}}
  ]
}}

EXAMPLE SYNCHRONIZED STRUCTURE:
Script: "Let's explore the concept of derivatives. [PAUSE] A derivative represents the rate of change. [PAUSE] We can visualize this as the slope of a tangent line."

Manim Code:
```python
from manimlib import *

class DerivativeVisualization(Scene):
    def construct(self):
        # Segment 1: Introduction (0-5 seconds)
        title = Text("Derivatives", font_size=48)
        self.play(Write(title), run_time=2)
        self.wait(3)  # Match "Let's explore the concept of derivatives" + pause
        
        # Segment 2: Definition (5-13 seconds) 
        definition = Text("Rate of Change", font_size=36)
        self.play(Transform(title, definition), run_time=2)
        self.wait(6)  # Match "A derivative represents..." + pause
        
        # Segment 3: Visualization (13-25 seconds)
        # Create function and tangent line
        axes = Axes(x_range=[-3, 3], y_range=[-2, 4])
        func = axes.get_graph(lambda x: x**2, color=BLUE)
        tangent = axes.get_graph(lambda x: 2*x, color=RED)

        self.play(ShowCreation(axes), run_time=2)
        self.play(ShowCreation(func), run_time=3)
        self.play(ShowCreation(tangent), run_time=3)
        self.wait(4)  # Match remaining narration
```

Generate content that follows this synchronized approach exactly.
"""

        # Generate content with retry logic
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                response = self.model.generate_content(user_prompt)
                output = response.text.strip().strip("```json").strip("```").strip()
                data = json.loads(output)
                
                # Validate required fields
                required_fields = ["script", "manim_code", "estimated_duration"]
                if all(field in data for field in required_fields):
                    # Clean the manim code (remove markdown blocks)
                    data["manim_code"] = self._clean_manim_code(data["manim_code"])

                    # Estimate actual speech duration
                    actual_duration = self.estimate_speech_duration(data["script"])
                    data["actual_speech_duration"] = actual_duration
                    
                    # Check if duration is reasonable
                    duration_diff = abs(actual_duration - target_duration)
                    if duration_diff > 10:  # More than 10 seconds off
                        print(f"⚠️ Duration mismatch: target={target_duration}s, actual={actual_duration}s")
                        if attempt < max_attempts - 1:
                            user_prompt += f"\n\nADJUST SCRIPT LENGTH: Current estimated duration is {actual_duration}s, but target is {target_duration}s. Make script {'shorter' if actual_duration > target_duration else 'longer'}."
                            continue
                    
                    print(f"✅ Synchronized content generated (attempt {attempt + 1})")
                    print(f"📊 Target duration: {target_duration}s")
                    print(f"📊 Estimated speech duration: {actual_duration}s")
                    
                    # Save synchronized content
                    self._save_synchronized_content(data, job_id)
                    return data
                    
            except Exception as e:
                error_str = str(e)

                # Check if it's a quota error and try backup key
                if "429" in error_str and "quota" in error_str.lower():
                    print("🔄 Quota exceeded, switching to backup API key...")
                    try:
                        backup_result = self._retry_with_backup_key(user_prompt, target_duration)
                        if backup_result:
                            return backup_result
                    except Exception as backup_error:
                        print(f"❌ Backup key also failed: {backup_error}")

                if attempt < max_attempts - 1:
                    print(f"⚠️ Generation failed on attempt {attempt + 1}: {e}")
                    continue
                else:
                    raise e
        
        raise ValueError("Failed to generate synchronized content after all attempts")

    def _clean_manim_code(self, code: str) -> str:
        """Clean manim code by removing markdown blocks and fixing formatting"""
        # Remove markdown code blocks
        code = re.sub(r'^```python\s*\n', '', code, flags=re.MULTILINE)
        code = re.sub(r'^```\s*$', '', code, flags=re.MULTILINE)
        code = re.sub(r'```.*?```', '', code, flags=re.DOTALL)

        # Remove any remaining backticks
        code = code.replace('`', '')

        # Clean up extra whitespace
        lines = code.split('\n')
        cleaned_lines = []
        for line in lines:
            line = line.rstrip()
            if line or (cleaned_lines and cleaned_lines[-1]):  # Keep non-empty lines and single empty lines
                cleaned_lines.append(line)

        # Remove trailing empty lines
        while cleaned_lines and not cleaned_lines[-1]:
            cleaned_lines.pop()

        return '\n'.join(cleaned_lines)

    def _retry_with_backup_key(self, user_prompt: str, target_duration: float) -> Dict:
        """Retry generation with backup API key"""
        global current_api_key, backup_key

        if backup_key and current_api_key != backup_key:
            try:
                print("🔄 Switching to backup API key...")

                # Switch to backup key
                current_api_key = backup_key
                genai.configure(api_key=current_api_key)

                # Recreate model with new key
                backup_model = genai.GenerativeModel("gemini-1.5-flash")

                # Retry generation
                response = backup_model.generate_content(user_prompt)
                output = response.text.strip().strip("```json").strip("```").strip()
                data = json.loads(output)

                # Validate required fields
                required_fields = ["script", "manim_code", "estimated_duration"]
                if all(field in data for field in required_fields):
                    # Clean the manim code
                    data["manim_code"] = self._clean_manim_code(data["manim_code"])

                    # Estimate actual speech duration
                    actual_duration = self.estimate_speech_duration(data["script"])
                    data["actual_speech_duration"] = actual_duration

                    print("✅ Successfully generated content with backup key!")
                    return data

            except Exception as e:
                print(f"❌ Backup key generation failed: {e}")

                # Switch back to primary key for future attempts
                current_api_key = primary_key
                genai.configure(api_key=current_api_key)

        return None

    def _extract_search_terms(self, prompt: str) -> str:
        """Extract key terms for RAG search"""
        # Simple keyword extraction - could be enhanced with NLP
        math_terms = ["derivative", "integral", "function", "graph", "vector", "matrix", 
                     "equation", "theorem", "proof", "limit", "series", "transform"]
        
        terms = []
        prompt_lower = prompt.lower()
        for term in math_terms:
            if term in prompt_lower:
                terms.append(term)
        
        # Add the original prompt for broader search
        terms.append(prompt)
        return " ".join(terms)
    
    def _save_synchronized_content(self, data: Dict, job_id: str):
        """Save synchronized content with timing information"""
        base_path = Path(f"generated/{job_id}")
        base_path.mkdir(parents=True, exist_ok=True)
        
        # Save script
        with open(base_path / "script.txt", "w", encoding="utf-8") as f:
            f.write(data["script"])
        
        # Save manim code
        with open(base_path / "manim_code.py", "w", encoding="utf-8") as f:
            f.write(data["manim_code"])
        
        # Save timing information
        timing_info = {
            "estimated_duration": data.get("estimated_duration", 0),
            "actual_speech_duration": data.get("actual_speech_duration", 0),
            "timing_segments": data.get("timing_segments", [])
        }
        
        with open(base_path / "timing_info.json", "w", encoding="utf-8") as f:
            json.dump(timing_info, f, indent=2)
        
        print(f"📁 Synchronized content saved to: {base_path}")


def generate_synchronized_video(prompt: str, job_id: str, target_duration: float = 30.0) -> Dict:
    """Main function to generate synchronized video content"""
    generator = SynchronizedContentGenerator()
    return generator.generate_synchronized_content(prompt, job_id, target_duration)


if __name__ == "__main__":
    # Test the synchronized generator
    test_prompt = "visualizing the derivative as the slope of a tangent line"
    test_job_id = "sync_test"
    
    try:
        result = generate_synchronized_video(test_prompt, test_job_id, target_duration=25.0)
        print("✅ Synchronized generation test successful!")
        print(f"📊 Estimated duration: {result.get('estimated_duration')}s")
        print(f"📊 Speech duration: {result.get('actual_speech_duration')}s")
    except Exception as e:
        print(f"❌ Test failed: {e}")
