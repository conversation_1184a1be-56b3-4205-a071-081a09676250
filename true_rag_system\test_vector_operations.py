"""
Test the True RAG system with vector addition and subtraction
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_llm import TrueRAGEnhancedManimLLM

def test_vector_addition():
    """Test vector addition generation"""
    
    print("🔢 Testing Vector Addition")
    print("=" * 50)
    
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    topic = "vector addition in 2D"
    
    print(f"🎯 Generating: {topic}")
    
    try:
        result = llm.generate_audio_first_video(topic)
        
        if result.get('success'):
            print(f"✅ Generation successful!")
            
            # Show context analysis
            context = result['context_analysis']
            print(f"🧠 Primary Focus: {context.primary_focus}")
            print(f"📊 Complexity: {context.complexity_score:.2f}")
            print(f"🎯 Math Concepts: {len(context.mathematical_concepts)}")
            
            # Save code
            code_file = Path("vector_addition_code.py")
            with open(code_file, 'w') as f:
                f.write(result['manim_code'])
            print(f"💾 Code saved to: {code_file}")
            
            # Generate video
            print(f"🎬 Generating video...")
            video_path = generate_video(result['manim_code'], "vector_addition")
            
            return result, video_path
        else:
            print(f"❌ Generation failed: {result.get('error')}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

def test_vector_subtraction():
    """Test vector subtraction generation"""
    
    print("\n➖ Testing Vector Subtraction")
    print("=" * 50)
    
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    topic = "vector subtraction and difference"
    
    print(f"🎯 Generating: {topic}")
    
    try:
        result = llm.generate_audio_first_video(topic)
        
        if result.get('success'):
            print(f"✅ Generation successful!")
            
            # Show context analysis
            context = result['context_analysis']
            print(f"🧠 Primary Focus: {context.primary_focus}")
            print(f"📊 Complexity: {context.complexity_score:.2f}")
            print(f"🎯 Math Concepts: {len(context.mathematical_concepts)}")
            
            # Save code
            code_file = Path("vector_subtraction_code.py")
            with open(code_file, 'w') as f:
                f.write(result['manim_code'])
            print(f"💾 Code saved to: {code_file}")
            
            # Generate video
            print(f"🎬 Generating video...")
            video_path = generate_video(result['manim_code'], "vector_subtraction")
            
            return result, video_path
        else:
            print(f"❌ Generation failed: {result.get('error')}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

def generate_video(manim_code, job_prefix):
    """Generate video from manim code"""
    
    try:
        from app.manim_3b1b_runner import generate_video_3b1b
        import uuid
        
        job_id = f"{job_prefix}_{uuid.uuid4().hex[:8]}"
        
        video_path = generate_video_3b1b(
            manim_code=manim_code,
            job_id=job_id,
            scene_name="TrueRAGScene",
            output_format="mp4"
        )
        
        if video_path and Path(video_path).exists():
            print(f"✅ Video generated: {video_path}")
            file_size = Path(video_path).stat().st_size / (1024 * 1024)  # MB
            print(f"📊 File size: {file_size:.2f} MB")
            return video_path
        else:
            print(f"❌ Video generation failed")
            return None
            
    except Exception as e:
        print(f"❌ Video generation error: {e}")
        return None

def compare_vector_operations(addition_result, subtraction_result):
    """Compare the generated vector operations"""
    
    print("\n📊 Comparing Vector Operations")
    print("=" * 50)
    
    if not addition_result or not subtraction_result:
        print("❌ Cannot compare - one or both generations failed")
        return
    
    add_code = addition_result['manim_code']
    sub_code = subtraction_result['manim_code']
    
    # Analyze differences
    print("🔍 Code Analysis:")
    
    # Check for operation-specific keywords
    add_keywords = ['addition', 'add', '+', 'sum']
    sub_keywords = ['subtraction', 'subtract', '-', 'difference']
    
    add_found = [kw for kw in add_keywords if kw.lower() in add_code.lower()]
    sub_found = [kw for kw in sub_keywords if kw.lower() in sub_code.lower()]
    
    print(f"📈 Addition keywords found: {add_found}")
    print(f"📉 Subtraction keywords found: {sub_found}")
    
    # Check for different mathematical operations
    if 'v1_end[0] + v2_end[0]' in add_code:
        print("✅ Addition: Found vector component addition")
    if 'v1_end[0] - v2_end[0]' in sub_code:
        print("✅ Subtraction: Found vector component subtraction")
    
    # Calculate similarity
    add_lines = set(line.strip() for line in add_code.split('\n') if line.strip())
    sub_lines = set(line.strip() for line in sub_code.split('\n') if line.strip())
    
    intersection = len(add_lines.intersection(sub_lines))
    union = len(add_lines.union(sub_lines))
    similarity = (intersection / union) * 100 if union > 0 else 0
    
    print(f"📊 Code similarity: {similarity:.1f}%")
    
    if similarity < 70:
        print("✅ SUCCESS: Addition and subtraction generate different code!")
    else:
        print("⚠️ WARNING: Addition and subtraction are too similar")

def analyze_vector_code_quality(code, operation_type):
    """Analyze the quality of vector operation code"""
    
    print(f"\n🔍 Analyzing {operation_type} Code Quality")
    print("-" * 40)
    
    quality_checks = {
        'has_arrows': 'Arrow(' in code,
        'has_vectors': 'vector' in code.lower(),
        'has_coordinates': any(coord in code for coord in ['[', ']', ',']),
        'has_labels': 'Text(' in code and ('v1' in code or 'v2' in code),
        'has_result': 'result' in code.lower() or 'sum' in code.lower() or 'difference' in code.lower(),
        'has_components': any(comp in code for comp in ['v1_end', 'v2_end', 'result_end']),
        'proper_cleanup': 'FadeOut' in code
    }
    
    passed = sum(quality_checks.values())
    total = len(quality_checks)
    
    print(f"📊 Quality Score: {passed}/{total} ({passed/total*100:.1f}%)")
    
    for check, result in quality_checks.items():
        status = "✅" if result else "❌"
        print(f"   {status} {check.replace('_', ' ').title()}")
    
    return passed / total

def test_vector_variations():
    """Test different vector operation variations"""
    
    print("\n🎯 Testing Vector Operation Variations")
    print("=" * 50)
    
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    
    variations = [
        "vector addition with components",
        "vector subtraction visualization", 
        "adding two vectors graphically",
        "subtracting vectors using parallelogram method"
    ]
    
    results = []
    
    for i, variation in enumerate(variations, 1):
        print(f"\n🎬 Test {i}: {variation}")
        print("-" * 30)
        
        try:
            result = llm.generate_audio_first_video(variation)
            
            if result.get('success'):
                context = result['context_analysis']
                print(f"✅ Generated successfully")
                print(f"🧠 Focus: {context.primary_focus}")
                print(f"📊 Complexity: {context.complexity_score:.2f}")
                
                # Quick code analysis
                code = result['manim_code']
                has_addition = any(op in code for op in ['+', 'addition', 'add'])
                has_subtraction = any(op in code for op in ['-', 'subtraction', 'subtract'])
                
                print(f"🔍 Contains addition: {'✅' if has_addition else '❌'}")
                print(f"🔍 Contains subtraction: {'✅' if has_subtraction else '❌'}")
                
                results.append({
                    'variation': variation,
                    'result': result,
                    'has_addition': has_addition,
                    'has_subtraction': has_subtraction
                })
            else:
                print(f"❌ Generation failed")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    return results

def main():
    """Main test function"""
    
    print("🚀 Testing True RAG Vector Operations")
    print("=" * 70)
    print("Testing vector addition and subtraction to verify")
    print("topic-specific generation for different operations.")
    print("=" * 70)
    
    # Test 1: Vector Addition
    add_result, add_video = test_vector_addition()
    
    # Test 2: Vector Subtraction  
    sub_result, sub_video = test_vector_subtraction()
    
    # Test 3: Compare operations
    if add_result and sub_result:
        compare_vector_operations(add_result, sub_result)
        
        # Analyze code quality
        add_quality = analyze_vector_code_quality(add_result['manim_code'], "Addition")
        sub_quality = analyze_vector_code_quality(sub_result['manim_code'], "Subtraction")
    
    # Test 4: Test variations
    variations = test_vector_variations()
    
    # Summary
    print(f"\n🎉 Vector Operations Test Summary")
    print("=" * 50)
    
    if add_video:
        print(f"✅ Vector Addition: {add_video}")
    if sub_video:
        print(f"✅ Vector Subtraction: {sub_video}")
    
    if add_result and sub_result:
        print(f"📊 Addition Quality: {add_quality*100:.1f}%")
        print(f"📊 Subtraction Quality: {sub_quality*100:.1f}%")
    
    print(f"🎯 Variations Tested: {len(variations)}")
    
    success_count = sum(1 for v in variations if v.get('result', {}).get('success'))
    print(f"✅ Successful Generations: {success_count}/{len(variations)}")
    
    if add_video and sub_video:
        print(f"\n🎉 SUCCESS: True RAG generates different vector operations!")
        print(f"✅ Vector addition and subtraction working")
        print(f"✅ Topic-specific content generation confirmed")
        print(f"✅ Real video output for both operations")

if __name__ == "__main__":
    main()
