#!/usr/bin/env python3
"""
Enhanced Mathematical Animation Generator
Focus on rich mathematical visualizations instead of text displays
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv

# Import our enhanced audio-first modules
from app.audio_first_generator import AudioFirstGenerator
from app.manim_3b1b_runner import generate_video_3b1b, check_3b1b_manim_installation
from app.video_merger import merge_audio_video

load_dotenv()


def generate_enhanced_mathematical_video(topic: str, job_id: str = None) -> dict:
    """
    Generate video with enhanced mathematical animations
    
    Args:
        topic: Mathematical topic to visualize
        job_id: Unique identifier (auto-generated if None)
        
    Returns:
        Dictionary with results and file paths
    """
    
    # Generate job ID if not provided
    if job_id is None:
        import uuid
        job_id = f"enhanced_{str(uuid.uuid4())[:8]}"
    
    print("🎨 ENHANCED MATHEMATICAL ANIMATION GENERATOR")
    print("=" * 60)
    print(f"📋 Topic: {topic}")
    print(f"🆔 Job ID: {job_id}")
    print("🎯 Focus: Rich mathematical visualizations")
    print()
    
    results = {
        "success": False,
        "job_id": job_id,
        "topic": topic,
        "files": {},
        "errors": [],
        "animation_quality": "unknown"
    }
    
    try:
        # Step 1: Generate audio-first content with enhanced prompts
        print("🎵 Step 1: Generating Audio-First Content...")
        print("-" * 50)
        
        generator = AudioFirstGenerator()
        content_result = generator.generate_audio_first_content(topic, job_id)
        
        if not content_result:
            raise Exception("Audio-first content generation failed")
        
        print(f"✅ Content generated successfully")
        print(f"🎵 Audio Duration: {content_result.get('natural_duration', 0):.1f}s")
        print(f"📊 Audio Segments: {len(content_result.get('segments', []))}")
        print()
        
        # Step 2: Analyze generated code for mathematical content
        print("🔍 Step 2: Analyzing Mathematical Content...")
        print("-" * 50)
        
        code_path = content_result.get('code_path', f"generated/{job_id}/manim_code.py")
        if not os.path.exists(code_path):
            raise Exception(f"Generated code file not found: {code_path}")
        
        with open(code_path, 'r', encoding='utf-8') as f:
            manim_code = f.read()
        
        # Analyze animation quality
        animation_analysis = analyze_animation_quality(manim_code)
        results["animation_quality"] = animation_analysis
        
        print(f"📊 Animation Analysis:")
        print(f"   • Mathematical Objects: {animation_analysis['math_objects']}")
        print(f"   • Text-Only Segments: {animation_analysis['text_only_segments']}")
        print(f"   • Rich Animations: {animation_analysis['rich_animations']}")
        print(f"   • Quality Score: {animation_analysis['quality_score']:.1f}/10")
        print()
        
        # Step 3: Generate video
        print("🎬 Step 3: Generating Enhanced Video...")
        print("-" * 50)
        
        video_path = generate_video_3b1b(manim_code, job_id, output_format="mp4")
        
        if not video_path or not os.path.exists(video_path):
            raise Exception("Video generation failed - no output file")
        
        print(f"✅ Video generated: {video_path}")
        print()
        
        # Step 4: Create final integrated video
        print("🔗 Step 4: Creating Final Video...")
        print("-" * 50)
        
        audio_path = content_result.get('audio_path')
        final_video_path = video_path
        
        if audio_path and os.path.exists(audio_path):
            try:
                final_video_path = merge_audio_video(
                    audio_path, 
                    video_path, 
                    job_id,
                    output_name="enhanced_mathematical_video"
                )
                print(f"✅ Integrated video created: {final_video_path}")
            except Exception as merge_error:
                print(f"⚠️ Audio merge failed: {merge_error}")
                print("📹 Using video-only output")
        
        # Success!
        results["success"] = True
        results["files"] = {
            "final_video": final_video_path,
            "video": video_path,
            "audio": content_result.get('audio_path', ''),
            "script": content_result.get('script_path', f"generated/{job_id}/script.txt"),
            "code": code_path
        }
        
        print()
        print("🎉 ENHANCED MATHEMATICAL VIDEO COMPLETED!")
        print(f"📊 Animation Quality: {animation_analysis['quality_score']:.1f}/10")
        print(f"🎯 Mathematical Focus: {animation_analysis['math_focus']:.1f}%")
        
        return results
        
    except Exception as e:
        error_msg = str(e)
        print(f"❌ Generation failed: {error_msg}")
        results["errors"].append(error_msg)
        return results


def analyze_animation_quality(code: str) -> dict:
    """Analyze the quality of mathematical animations in the code"""
    
    analysis = {
        "math_objects": 0,
        "text_only_segments": 0,
        "rich_animations": 0,
        "quality_score": 0.0,
        "math_focus": 0.0
    }
    
    lines = code.split('\n')
    total_segments = 0
    
    # Count mathematical objects
    math_patterns = [
        'axes.get_graph(',
        'Circle(',
        'Rectangle(',
        'Line(',
        'Dot(',
        'Triangle(',
        'Square(',
        'Polygon('
    ]
    
    # Count animations
    animation_patterns = [
        'ShowCreation(',
        'Transform(',
        'FadeIn(',
        'FadeOut(',
        'Write('
    ]
    
    # Count text-only patterns
    text_only_patterns = [
        'Text(',
        'Write('
    ]
    
    for line in lines:
        line = line.strip()
        
        # Count segments
        if '# Segment' in line:
            total_segments += 1
        
        # Count mathematical objects
        for pattern in math_patterns:
            if pattern in line:
                analysis["math_objects"] += 1
        
        # Count rich animations
        for pattern in animation_patterns:
            if pattern in line and not any(text_pattern in line for text_pattern in text_only_patterns):
                analysis["rich_animations"] += 1
        
        # Count text-only segments
        if 'Text(' in line and 'self.play(Write(' in line:
            # Check if this segment has mathematical content
            has_math = any(pattern in line for pattern in math_patterns)
            if not has_math:
                analysis["text_only_segments"] += 1
    
    # Calculate quality score
    if total_segments > 0:
        math_ratio = analysis["math_objects"] / max(total_segments, 1)
        text_ratio = analysis["text_only_segments"] / total_segments
        
        analysis["quality_score"] = min(10.0, (math_ratio * 8) + (1 - text_ratio) * 2)
        analysis["math_focus"] = (1 - text_ratio) * 100
    
    return analysis


def main():
    """Main function for enhanced mathematical animation generation"""
    
    print("🎨 ENHANCED MATHEMATICAL ANIMATION GENERATOR")
    print("=" * 70)
    print("🎯 Generating rich mathematical visualizations")
    print("📊 Minimizing text-only displays")
    print()
    
    # Environment validation
    if not os.getenv("GOOGLE_API_KEY") and not os.getenv("GOOGLE_API_KEY2"):
        print("❌ No Google API keys found in environment")
        sys.exit(1)
    
    if not check_3b1b_manim_installation():
        print("❌ 3Blue1Brown manim not found")
        sys.exit(1)
    
    print("✅ Environment validation passed")
    print()
    
    # Get mathematical topic
    print("🎯 MATHEMATICAL TOPICS FOR RICH ANIMATIONS:")
    print("   • Quadratic functions and parabolas")
    print("   • Linear functions and slope")
    print("   • Trigonometry and unit circle")
    print("   • Derivatives and tangent lines")
    print("   • Geometric transformations")
    print("   • Area under curves")
    print()
    
    topic = input("Enter mathematical topic: ").strip()
    if not topic:
        topic = "quadratic functions and vertex form"
        print(f"Using default topic: {topic}")
    
    print()
    
    # Generate enhanced video
    start_time = time.time()
    results = generate_enhanced_mathematical_video(topic)
    end_time = time.time()
    
    # Display results
    print()
    print("📊 GENERATION RESULTS")
    print("=" * 40)
    
    if results["success"]:
        print("✅ Status: SUCCESS")
        print(f"⏱️ Generation Time: {end_time - start_time:.1f} seconds")
        
        if results["animation_quality"] != "unknown":
            quality = results["animation_quality"]
            print(f"📊 Animation Quality Analysis:")
            print(f"   • Quality Score: {quality['quality_score']:.1f}/10")
            print(f"   • Mathematical Focus: {quality['math_focus']:.1f}%")
            print(f"   • Math Objects: {quality['math_objects']}")
            print(f"   • Rich Animations: {quality['rich_animations']}")
            print(f"   • Text-Only Segments: {quality['text_only_segments']}")
        
        print(f"📁 Generated Files:")
        for file_type, path in results["files"].items():
            if path and os.path.exists(path):
                size_mb = os.path.getsize(path) / (1024 * 1024)
                print(f"   • {file_type}: {path} ({size_mb:.1f} MB)")
        
        print()
        print("🎯 Quality Assessment:")
        if results["animation_quality"]["quality_score"] >= 8.0:
            print("🌟 EXCELLENT: Rich mathematical animations generated!")
        elif results["animation_quality"]["quality_score"] >= 6.0:
            print("✅ GOOD: Decent mathematical content with some improvements needed")
        else:
            print("⚠️ NEEDS IMPROVEMENT: Too much text, not enough mathematical animations")
        
    else:
        print("❌ Status: FAILED")
        for error in results["errors"]:
            print(f"   • {error}")
    
    print()
    print("🎯 Next Steps:")
    if results["success"]:
        print("1. Review the generated video for mathematical animation quality")
        print("2. Check if animations match the voice explanations")
        print("3. Verify that text fades quickly and doesn't dominate")
        print("4. Test with different mathematical topics")
    else:
        print("1. Check the error messages above")
        print("2. Verify API keys and environment setup")
        print("3. Try with a simpler mathematical topic")


if __name__ == "__main__":
    main()
