# 3Blue1Brown Video Generator - React Frontend

A modern, practical React frontend for the 3Blue1Brown Video Generator API.

## Features

- **Clean, Modern UI** - Professional design with smooth animations
- **Real-time Progress** - Live updates during video generation
- **Job History** - Track all your generated videos
- **Easy Downloads** - One-click download for all file types
- **Responsive Design** - Works on desktop and mobile
- **Example Topics** - Quick-start with suggested mathematical topics

## Setup Instructions

### Prerequisites
- Node.js 16+ installed
- API server running on http://localhost:8000

### Installation

1. **Navigate to frontend directory:**
```bash
cd frontend_react
```

2. **Install dependencies:**
```bash
npm install
```

3. **Start development server:**
```bash
npm start
```

4. **Open in browser:**
```
http://localhost:3000
```

## Usage

### Generate Videos
1. Enter a mathematical topic (e.g., "derivatives and tangent lines")
2. Select output format (MP4 recommended)
3. Click "Generate Video"
4. Monitor real-time progress
5. Download generated files when complete

### View History
- Switch to "Job History" tab
- See all previous generations
- Download files from completed jobs
- Check status of ongoing generations

## File Structure

```
frontend_react/
├── public/
│   └── index.html          # HTML template
├── src/
│   ├── components/
│   │   ├── Header.js       # App header with logo
│   │   ├── Header.css
│   │   ├── VideoGenerator.js  # Main generation form
│   │   ├── VideoGenerator.css
│   │   ├── JobHistory.js   # History and downloads
│   │   └── JobHistory.css
│   ├── App.js              # Main app component
│   ├── App.css
│   ├── index.js            # React entry point
│   └── index.css           # Global styles
├── package.json            # Dependencies and scripts
└── README.md              # This file
```

## API Integration

The frontend communicates with the backend API through:

- **POST /generate** - Start video generation
- **GET /status/{job_id}** - Check generation progress
- **GET /download/{job_id}/{file_type}** - Download files

## Technologies Used

- **React 18** - Modern React with hooks
- **Axios** - HTTP client for API calls
- **Lucide React** - Beautiful, consistent icons
- **CSS3** - Modern styling with gradients and animations

## Development

### Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm eject` - Eject from Create React App

### Customization

- **Colors**: Edit CSS custom properties in `index.css`
- **Layout**: Modify component styles in respective CSS files
- **Features**: Add new components in `src/components/`

## Production Build

```bash
npm run build
```

This creates an optimized build in the `build/` folder ready for deployment.

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Ensure backend server is running on port 8000
   - Check CORS settings in backend

2. **Downloads Not Working**
   - Verify download endpoints are accessible
   - Check browser download permissions

3. **Styling Issues**
   - Clear browser cache
   - Check for CSS conflicts

### Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+
