"""
Test the improved True RAG system with topic-specific code generation
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_llm import TrueRAGEnhancedManimLLM

def test_topic_specific_generation():
    """Test that different topics generate different, appropriate code"""
    
    print("🎯 Testing Topic-Specific Code Generation")
    print("=" * 60)
    
    # Initialize the True RAG Enhanced LLM
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    
    # Test different topics that should generate different code
    test_topics = [
        "derivatives and tangent lines",
        "vector addition in 2D", 
        "area under a curve",
        "pythagorean theorem proof"
    ]
    
    results = {}
    
    for i, topic in enumerate(test_topics, 1):
        print(f"\n🎬 Test {i}: {topic}")
        print("-" * 40)
        
        try:
            # Generate using True RAG
            result = llm.generate_audio_first_video(topic)
            
            if result.get('success'):
                code = result['manim_code']
                context = result['context_analysis']
                
                print(f"✅ Generation successful!")
                print(f"🧠 Primary Focus: {context.primary_focus}")
                print(f"📊 Complexity: {context.complexity_score:.2f}")
                print(f"🎯 Math Concepts: {len(context.mathematical_concepts)}")
                
                # Analyze the generated code for topic-specific content
                code_analysis = analyze_code_content(code, topic)
                print(f"📝 Code Analysis:")
                for key, value in code_analysis.items():
                    print(f"   - {key}: {value}")
                
                # Save code for inspection
                safe_topic = topic.replace(' ', '_').replace('/', '_')
                code_file = Path(f"improved_{safe_topic}.py")
                with open(code_file, 'w') as f:
                    f.write(code)
                print(f"💾 Code saved to: {code_file}")
                
                results[topic] = {
                    'code': code,
                    'context': context,
                    'analysis': code_analysis
                }
                
            else:
                print(f"❌ Generation failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    # Compare results
    print(f"\n📊 Cross-Topic Analysis:")
    print("=" * 40)
    
    if len(results) >= 2:
        topics = list(results.keys())
        
        # Check if codes are different
        codes_different = True
        for i in range(len(topics)):
            for j in range(i+1, len(topics)):
                topic1, topic2 = topics[i], topics[j]
                code1 = results[topic1]['code']
                code2 = results[topic2]['code']
                
                # Simple similarity check
                similarity = calculate_code_similarity(code1, code2)
                print(f"📈 Similarity between '{topic1}' and '{topic2}': {similarity:.1f}%")
                
                if similarity > 80:
                    codes_different = False
        
        if codes_different:
            print("✅ SUCCESS: Different topics generate different code!")
        else:
            print("❌ ISSUE: Topics are generating too similar code")
    
    return results

def analyze_code_content(code: str, topic: str) -> dict:
    """Analyze code content for topic-specific elements"""
    
    analysis = {
        'topic_keywords_found': [],
        'mathematical_objects': [],
        'specific_functions': [],
        'topic_relevance': 'Unknown'
    }
    
    code_lower = code.lower()
    
    # Check for topic-specific keywords
    topic_keywords = {
        'derivative': ['derivative', 'tangent', 'slope', "f'", 'get_tangent_line'],
        'vector': ['vector', 'arrow', 'addition', 'component'],
        'area': ['area', 'integral', 'riemann', 'rectangle', 'get_area'],
        'pythagorean': ['pythagorean', 'theorem', 'triangle', 'square', 'polygon']
    }
    
    for category, keywords in topic_keywords.items():
        found_keywords = [kw for kw in keywords if kw in code_lower]
        if found_keywords:
            analysis['topic_keywords_found'].extend(found_keywords)
            if category in topic.lower():
                analysis['topic_relevance'] = 'High'
    
    # Check for mathematical objects
    math_objects = ['Axes', 'Arrow', 'Polygon', 'Square', 'Dot', 'get_graph', 'get_tangent_line', 'get_area']
    analysis['mathematical_objects'] = [obj for obj in math_objects if obj in code]
    
    # Check for specific mathematical functions
    if 'lambda x:' in code:
        # Extract lambda functions
        import re
        lambda_matches = re.findall(r'lambda x: ([^,)]+)', code)
        analysis['specific_functions'] = lambda_matches
    
    # Determine relevance if not already set
    if analysis['topic_relevance'] == 'Unknown':
        if len(analysis['topic_keywords_found']) > 0:
            analysis['topic_relevance'] = 'Medium'
        else:
            analysis['topic_relevance'] = 'Low'
    
    return analysis

def calculate_code_similarity(code1: str, code2: str) -> float:
    """Calculate similarity between two code strings"""
    
    # Simple line-by-line similarity
    lines1 = set(line.strip() for line in code1.split('\n') if line.strip())
    lines2 = set(line.strip() for line in code2.split('\n') if line.strip())
    
    if not lines1 or not lines2:
        return 0.0
    
    intersection = len(lines1.intersection(lines2))
    union = len(lines1.union(lines2))
    
    return (intersection / union) * 100 if union > 0 else 0.0

def test_specific_video_generation():
    """Test generating actual videos with the improved system"""
    
    print("\n🎬 Testing Improved Video Generation")
    print("=" * 60)
    
    # Test one topic in detail
    topic = "derivatives and tangent lines"
    
    llm = TrueRAGEnhancedManimLLM("true_rag_db")
    result = llm.generate_audio_first_video(topic)
    
    if result.get('success'):
        print(f"✅ Generated improved code for: {topic}")
        
        # Try to generate video
        try:
            from app.manim_3b1b_runner import generate_video_3b1b
            import uuid
            
            job_id = f"improved_{uuid.uuid4().hex[:8]}"
            
            video_path = generate_video_3b1b(
                manim_code=result['manim_code'],
                job_id=job_id,
                scene_name="TrueRAGScene",
                output_format="mp4"
            )
            
            if video_path and Path(video_path).exists():
                print(f"✅ Video generated successfully!")
                print(f"📹 Video file: {video_path}")
                
                # Get file size
                file_size = Path(video_path).stat().st_size / (1024 * 1024)  # MB
                print(f"📊 File size: {file_size:.2f} MB")
                
                return video_path
            else:
                print(f"❌ Video generation failed")
                
        except Exception as e:
            print(f"❌ Video generation error: {e}")
    
    return None

def main():
    """Main test function"""
    
    print("🚀 Testing Improved True RAG System")
    print("=" * 70)
    print("Testing topic-specific code generation to ensure different")
    print("topics produce different, appropriate mathematical animations.")
    print("=" * 70)
    
    # Test 1: Topic-specific code generation
    results = test_topic_specific_generation()
    
    # Test 2: Generate actual video with improved system
    if results:
        video_path = test_specific_video_generation()
        
        if video_path:
            print(f"\n🎉 SUCCESS: Improved True RAG system working!")
            print(f"✅ Topic-specific code generation: Working")
            print(f"✅ Video generation: Working")
            print(f"📹 Sample video: {video_path}")
        else:
            print(f"\n⚠️ Code generation improved, but video generation failed")
    
    print("\n🎯 Improvement Summary:")
    print("- Fixed generic parabola issue")
    print("- Added topic-specific code generation")
    print("- Each topic now generates appropriate animations")
    print("- Context analysis properly drives code generation")

if __name__ == "__main__":
    main()
