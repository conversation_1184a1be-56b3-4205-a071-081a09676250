import os
import json
import google.generativeai as genai
from pathlib import Path
from dotenv import load_dotenv

load_dotenv()

genai.configure(api_key=os.getenv("GOOGLE_API_KEY2"))

model = genai.GenerativeModel("gemini-1.5-flash")

def generate_script_and_manim_code(prompt: str, job_id: str):
    user_prompt = f"""
    You are a Manim expert and a video scriptwriter. Generate a fully synchronized voiceover script and a matching Manim animation using **Manim Community Edition** (latest stable version only). Return output in strict JSON format:

{{
  "script": "...",
  "manim_code": "..."
}}

### OBJECTIVE:
Create a video tutorial on a math or computer science topic that explains a concept visually using Manim. The narration script and animation must stay in sync — each sentence or paragraph should have matching visual movement or transformation.

---

### HARD REQUIREMENTS:

1. 🗣️ **SCRIPT**:
   - The script should follow natural speaking pace (≈130–150 wpm).
   - Each block of narration must clearly match an animated scene.
   - Keep the tone educational and clear.

2. 🎞️ **MANIM CODE**:
   - Use **only Manim Community Edition built-ins**. No external dependencies like NumPy, Matplotlib, SciPy, etc.
   - Use only the following valid Manim imports:
     `from manim import *`
   - Do not import or reference any third-party Python libraries.
   - Use animation tools like: `Create`, `FadeIn`, `Transform`, `Write`, `.animate`, `Tex`, `MathTex`, `VGroup`, `ValueTracker`, `NumberPlane`, `Axes`, `always_redraw`, `add_updater`, etc.
   - Sync animation with narration using:
     - `run_time` in `self.play(...)`
     - `self.wait(seconds)` between segments
   - Total duration of the animation must closely match the estimated voiceover duration and you have to keep in mind that 1 animation will take around 20 seconds of sound so animations should be that long.therefore for every sentence in script you have to make about 20 sec of animation.
   - Must run cleanly with:
     `manim -pql filename.py SceneName`

3. ✅ **NO** external packages like:
   - numpy, matplotlib, pandas, scipy, etc.
   - Only use what Manim CE provides.

4. 🧱 Organize code cleanly:
   - Use modular structures where appropriate (`VGroup`, helper methods).
   - Keep visuals simple, readable, and instructional.
   - Use LaTeX via `MathTex` or `Tex` when writing math.

---

### OUTPUT FORMAT:

Return only a single valid JSON object like this:

{{
  "script": "Narrator: First, we define a function. Then we observe its slope at different points...",
  "manim_code": "from manim import *\n\nclass MyScene(Scene):\n    def construct(self):\n        # animation code\n        ..."
}}

---

###TOPICS:
- {prompt}


Make the animation educational, clear, and visually rich.

---
DO NOT include explanations, markdown, or extra formatting — only return a single clean JSON object.


    """

    response = model.generate_content(user_prompt)

    # Handle response
    try:
        # Gemini may return Markdown — strip code block fences if present
        output = response.text.strip().strip("```json").strip("```").strip()
        data = json.loads(output)
    except Exception as e:
        raise ValueError(f"Failed to parse Gemini response: {e}\nRaw output:\n{response.text}")

    # Save output
    base_path = Path(f"generated/{job_id}")
    base_path.mkdir(parents=True, exist_ok=True)

    with open(base_path / "script.txt", "w", encoding="utf-8") as f:
        f.write(data["script"])

    with open(base_path / "manim_code.py", "w", encoding="utf-8") as f:
        f.write(data["manim_code"])

    return {
        "script": data["script"],
        "manim_code": data["manim_code"],
        "job_id": job_id
    }
