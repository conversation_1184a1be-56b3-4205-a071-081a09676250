.job-history {
  max-width: 900px;
  margin: 0 auto;
}

.job-history h2 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 28px;
}

.subtitle {
  color: #666;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.5;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-icon {
  color: #ccc;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #555;
  margin-bottom: 10px;
  font-size: 20px;
}

.empty-state p {
  font-size: 16px;
}

.jobs-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.job-item {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 20px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.job-item:hover {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.job-item.completed {
  border-left: 4px solid #28a745;
}

.job-item.failed {
  border-left: 4px solid #dc3545;
}

.job-item.processing {
  border-left: 4px solid #ffc107;
}

.job-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.job-info {
  flex: 1;
}

.job-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.job-title h4 {
  color: #2c3e50;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.status-icon {
  flex-shrink: 0;
}

.status-icon.completed {
  color: #28a745;
}

.status-icon.failed {
  color: #dc3545;
}

.status-icon.processing {
  color: #ffc107;
}

.status-icon.pending {
  color: #6c757d;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.job-meta {
  display: flex;
  gap: 15px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.job-meta span {
  font-size: 12px;
  color: #666;
  background: white;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.job-progress {
  font-size: 14px;
  color: #555;
  font-style: italic;
}

.job-error {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #dc3545;
  font-size: 14px;
  margin-top: 8px;
}

.job-downloads {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end;
}

.job-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: flex-end;
}

.preview-toggle-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
}

.preview-toggle-btn:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.download-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.download-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;
}

.download-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

/* Job Preview Section */
.job-preview-section {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  display: grid;
  gap: 20px;
}

.preview-item {
  background: white;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #e9ecef;
}

.preview-item h5 {
  color: #2c3e50;
  margin-bottom: 12px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.video-container {
  background: #000;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preview-video {
  width: 100%;
  height: auto;
  max-height: 300px;
  display: block;
}

.audio-container {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
}

.preview-audio {
  width: 100%;
  height: 40px;
}

.script-container {
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.preview-script {
  width: 100%;
  height: 150px;
  border: none;
  background: white;
  padding: 12px;
  font-family: 'Georgia', serif;
  line-height: 1.5;
  color: #333;
  font-size: 13px;
}

/* Responsive */
@media (max-width: 768px) {
  .job-history {
    max-width: 100%;
  }
  
  .job-main {
    flex-direction: column;
    gap: 15px;
  }
  
  .job-downloads {
    align-items: flex-start;
    width: 100%;
  }
  
  .download-buttons {
    width: 100%;
    justify-content: flex-start;
  }
  
  .job-meta {
    gap: 8px;
  }
  
  .job-title h4 {
    font-size: 16px;
  }

  .job-preview-section {
    padding: 15px;
    gap: 15px;
  }

  .preview-item {
    padding: 12px;
  }

  .preview-video {
    max-height: 200px;
  }

  .preview-script {
    height: 120px;
    font-size: 12px;
  }
}

/* Complete Video Styles for Job History */
.complete-video-item {
  border: 2px solid #4CAF50 !important;
  background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%) !important;
  position: relative;
}

.complete-video-item::before {
  content: "🎬 FINAL VIDEO";
  position: absolute;
  top: -10px;
  right: 15px;
  background: #4CAF50;
  color: white;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 11px;
  font-weight: bold;
  z-index: 1;
}

.complete-video-item h5 {
  color: #2E7D32 !important;
}

.preview-video.complete-video {
  border: 2px solid #4CAF50;
  border-radius: 8px;
}

.video-description {
  margin-top: 10px;
  color: #2E7D32;
  font-size: 13px;
  font-style: italic;
  text-align: center;
}

/* Download Button Primary Style */
.download-btn.primary {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  border: none;
  font-weight: 600;
  order: -1; /* Move to front */
}

.download-btn.primary:hover {
  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
  transform: translateY(-1px);
  box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
}
