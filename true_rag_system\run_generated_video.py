"""
<PERSON><PERSON><PERSON> to run the generated True RAG manim code and create a video
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

def run_manim_code():
    """Run the generated manim code"""
    
    print("🎬 Running Generated True RAG Manim Code")
    print("=" * 50)
    
    # Check if the generated code file exists
    code_file = Path("generated_true_rag_code.py")
    
    if not code_file.exists():
        print("❌ Generated code file not found. Run test_real_video.py first.")
        return
    
    print(f"📁 Found code file: {code_file}")
    
    # Read the generated code
    with open(code_file, 'r') as f:
        manim_code = f.read()
    
    print("📝 Generated code preview:")
    print("-" * 30)
    print(manim_code[:300] + "..." if len(manim_code) > 300 else manim_code)
    
    # Try to run with the manim runner
    try:
        from app.manim_3b1b_runner import generate_video_3b1b
        
        print("\n🎬 Generating video with 3Blue1Brown manim...")
        
        # Generate unique job ID
        import uuid
        job_id = str(uuid.uuid4())[:8]
        
        # Run the manim code
        video_path = generate_video_3b1b(
            manim_code=manim_code,
            job_id=job_id,
            scene_name="TrueRAGScene",
            output_format="mp4"
        )
        
        if video_path and Path(video_path).exists():
            print(f"✅ Video generated successfully!")
            print(f"📹 Video file: {video_path}")
            
            # Get file size
            file_size = Path(video_path).stat().st_size / (1024 * 1024)  # MB
            print(f"📊 File size: {file_size:.2f} MB")
            
            return video_path
        else:
            print(f"❌ Video generation failed - file not found: {video_path}")
            return None
            
    except ImportError as e:
        print(f"❌ Manim runner import failed: {e}")
        return None
    except Exception as e:
        print(f"❌ Video generation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_multiple_generated_codes():
    """Run all generated code files"""
    
    print("\n🎬 Running All Generated True RAG Codes")
    print("=" * 50)
    
    # Find all generated code files
    code_files = list(Path(".").glob("generated_code_*.py"))
    code_files.append(Path("generated_true_rag_code.py"))
    
    successful_videos = []
    
    for code_file in code_files:
        if not code_file.exists():
            continue
            
        print(f"\n📁 Processing: {code_file}")
        print("-" * 30)
        
        try:
            # Read the code
            with open(code_file, 'r') as f:
                manim_code = f.read()
            
            # Extract scene name from code
            scene_name = "TrueRAGScene"  # Default
            for line in manim_code.split('\n'):
                if 'class ' in line and '(Scene)' in line:
                    scene_name = line.split('class ')[1].split('(')[0].strip()
                    break
            
            print(f"🎯 Scene name: {scene_name}")
            
            # Generate video
            from app.manim_3b1b_runner import generate_video_3b1b
            import uuid
            
            job_id = f"true_rag_{uuid.uuid4().hex[:8]}"
            
            video_path = generate_video_3b1b(
                manim_code=manim_code,
                job_id=job_id,
                scene_name=scene_name,
                output_format="mp4"
            )
            
            if video_path and Path(video_path).exists():
                print(f"✅ Video generated: {video_path}")
                successful_videos.append(video_path)
            else:
                print(f"❌ Video generation failed")
                
        except Exception as e:
            print(f"❌ Error processing {code_file}: {e}")
    
    print(f"\n📊 Summary:")
    print(f"✅ Successful videos: {len(successful_videos)}")
    print(f"📁 Total code files: {len(code_files)}")
    
    if successful_videos:
        print(f"\n📹 Generated videos:")
        for video in successful_videos:
            print(f"  - {video}")
    
    return successful_videos

def test_code_quality():
    """Test the quality of generated code"""
    
    print("\n🔍 Testing Generated Code Quality")
    print("=" * 50)
    
    code_file = Path("generated_true_rag_code.py")
    
    if not code_file.exists():
        print("❌ No code file to test")
        return
    
    with open(code_file, 'r') as f:
        code = f.read()
    
    print("📊 Code Quality Analysis:")
    print("-" * 30)
    
    # Basic quality checks
    quality_score = 0
    total_checks = 0
    
    # Check 1: Has proper imports
    total_checks += 1
    if 'from manimlib import *' in code:
        print("✅ Proper imports")
        quality_score += 1
    else:
        print("❌ Missing proper imports")
    
    # Check 2: Has scene class
    total_checks += 1
    if 'class ' in code and '(Scene):' in code:
        print("✅ Scene class defined")
        quality_score += 1
    else:
        print("❌ No scene class found")
    
    # Check 3: Has construct method
    total_checks += 1
    if 'def construct(self):' in code:
        print("✅ Construct method defined")
        quality_score += 1
    else:
        print("❌ No construct method")
    
    # Check 4: Uses proper manim objects
    total_checks += 1
    manim_objects = ['Text', 'Axes', 'ShowCreation', 'Write', 'FadeOut']
    found_objects = [obj for obj in manim_objects if obj in code]
    if found_objects:
        print(f"✅ Uses manim objects: {', '.join(found_objects)}")
        quality_score += 1
    else:
        print("❌ No standard manim objects found")
    
    # Check 5: No obvious errors
    total_checks += 1
    error_patterns = ['Tex(', 'MathTex(', 'get_tangent_line(graph, x=']
    found_errors = [pattern for pattern in error_patterns if pattern in code]
    if not found_errors:
        print("✅ No obvious error patterns")
        quality_score += 1
    else:
        print(f"❌ Found error patterns: {', '.join(found_errors)}")
    
    # Calculate quality percentage
    quality_percentage = (quality_score / total_checks) * 100
    
    print(f"\n📊 Overall Quality Score: {quality_score}/{total_checks} ({quality_percentage:.1f}%)")
    
    if quality_percentage >= 80:
        print("🎉 High quality code!")
    elif quality_percentage >= 60:
        print("👍 Good quality code")
    else:
        print("⚠️ Code needs improvement")
    
    return quality_percentage

def main():
    """Main function"""
    
    print("🚀 True RAG Generated Video Runner")
    print("=" * 60)
    
    # Test 1: Run single generated code
    video_path = run_manim_code()
    
    # Test 2: Test code quality
    quality_score = test_code_quality()
    
    # Test 3: Run multiple codes (if available)
    if video_path:
        run_multiple_generated_codes()
    
    print("\n🎉 True RAG video generation testing completed!")
    
    if video_path:
        print(f"✅ Successfully generated video: {video_path}")
        print(f"📊 Code quality: {quality_score:.1f}%")
    else:
        print("❌ No videos were generated")

if __name__ == "__main__":
    main()
