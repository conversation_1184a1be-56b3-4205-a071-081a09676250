# FastAPI Server for RAG-Enhanced 3Blue1Brown Video Generator

🚀 **REST API interface for generating educational videos programmatically**

## 🌟 Features

- **RESTful API** for video generation
- **Asynchronous processing** with job tracking
- **Multiple output formats** (MP4/GIF)
- **File download endpoints** for generated content
- **Health monitoring** and status tracking
- **Interactive API documentation** (Swagger UI)

## 🚀 Quick Start

### 1. Start the API Server

```bash
# Activate virtual environment
.venv\Scripts\activate

# Start the FastAPI server
python api_server.py
```

The server will start on `http://localhost:8000`

### 2. Access API Documentation

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

### 3. Test with Client Script

```bash
# Interactive client
python api_client.py
```

## 📡 API Endpoints

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| `GET` | `/` | API information and available endpoints |
| `GET` | `/health` | Health check and system status |
| `POST` | `/generate` | Start video generation job |
| `GET` | `/status/{job_id}` | Get job status and progress |
| `GET` | `/download/{job_id}/{file_type}` | Download generated files |
| `GET` | `/jobs` | List all jobs |

### Detailed API Usage

#### 1. Generate Video

**POST** `/generate`

```json
{
  "topic": "derivatives and tangent lines",
  "output_format": "mp4",
  "job_id": "optional-custom-id"
}
```

**Response:**
```json
{
  "job_id": "abc12345",
  "status": "accepted",
  "message": "Video generation started",
  "topic": "derivatives and tangent lines",
  "output_format": "mp4",
  "created_at": "2024-01-15T10:30:00"
}
```

#### 2. Check Job Status

**GET** `/status/{job_id}`

**Response:**
```json
{
  "job_id": "abc12345",
  "status": "completed",
  "progress": "All files generated successfully",
  "topic": "derivatives and tangent lines",
  "output_format": "mp4",
  "created_at": "2024-01-15T10:30:00",
  "completed_at": "2024-01-15T10:32:30",
  "files": {
    "video": "generated/abc12345/DerivativeVisualization.mp4",
    "audio": "generated/abc12345/voiceover.mp3",
    "script": "generated/abc12345/script.txt",
    "code": "generated/abc12345/manim_code.py",
    "rag_examples": "generated/abc12345/rag_examples.txt"
  }
}
```

#### 3. Download Files

**GET** `/download/{job_id}/{file_type}`

Available file types: `video`, `audio`, `script`, `code`, `rag_examples`

## 🐍 Python Client Usage

```python
from api_client import VideoGeneratorClient

# Initialize client
client = VideoGeneratorClient("http://localhost:8000")

# Check health
health = client.health_check()
print(f"API Status: {health['status']}")

# Generate video
result = client.generate_video("quadratic functions", "mp4")
job_id = result['job_id']

# Wait for completion
final_status = client.wait_for_completion(job_id)

# Download video
client.download_file(job_id, "video", f"{job_id}_video.mp4")
```

## 🌐 cURL Examples

### Generate Video
```bash
curl -X POST "http://localhost:8000/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "linear algebra basics",
    "output_format": "mp4"
  }'
```

### Check Status
```bash
curl "http://localhost:8000/status/abc12345"
```

### Download Video
```bash
curl -O "http://localhost:8000/download/abc12345/video"
```

## 📊 Job Status States

| Status | Description |
|--------|-------------|
| `pending` | Job queued for processing |
| `processing` | Video generation in progress |
| `completed` | All files generated successfully |
| `failed` | Generation failed with error |

## 🔧 Configuration

### Environment Variables
```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional
API_HOST=0.0.0.0
API_PORT=8000
```

### Server Configuration
```python
# In api_server.py
uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 🚀 Production Deployment

### Using Uvicorn
```bash
uvicorn api_server:app --host 0.0.0.0 --port 8000 --workers 4
```

### Using Docker (Future Enhancement)
```dockerfile
FROM python:3.11
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
CMD ["uvicorn", "api_server:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 📁 File Structure

```
generated/
└── {job_id}/
    ├── script.txt              # Educational script
    ├── manim_code.py           # 3Blue1Brown manim code
    ├── voiceover.mp3           # TTS narration
    ├── rag_examples.txt        # Retrieved 3b1b examples
    └── {SceneName}.mp4         # Final video
```

## ⚠️ Important Notes

1. **RAG Database Required**: Run `python setup_3b1b_rag.py` before starting the API
2. **Local vs API**: The original `main_rag_3b1b.py` remains unchanged for local usage
3. **Job Persistence**: Current implementation uses in-memory storage (use Redis/DB for production)
4. **File Cleanup**: Generated files persist until manually cleaned
5. **Concurrent Jobs**: Multiple jobs can run simultaneously

## 🔍 Monitoring

### Health Check
```bash
curl http://localhost:8000/health
```

### List All Jobs
```bash
curl http://localhost:8000/jobs
```

## 🎯 Integration Examples

### JavaScript/Node.js
```javascript
const response = await fetch('http://localhost:8000/generate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    topic: 'calculus fundamentals',
    output_format: 'mp4'
  })
});
const result = await response.json();
```

### Python Requests
```python
import requests

response = requests.post('http://localhost:8000/generate', json={
    'topic': 'probability theory',
    'output_format': 'gif'
})
result = response.json()
```

This API provides a robust, scalable interface for generating educational videos while maintaining all the power and authenticity of the original 3Blue1Brown system!
