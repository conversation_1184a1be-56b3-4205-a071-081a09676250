import subprocess
from pathlib import Path

def generate_video(manim_code: str, job_id: str) -> str:    
    base_path = Path(f"generated/{job_id}")
    script_path = base_path / "manim_code.py"
    output_path = base_path / "animation.mp4"

    # Save Manim code already handled in llm.py

    command = [
        "manim", str(script_path),
        "Scene",  # Make sure the Scene class in code is called "Scene"
        "-q", "l", "--format", "mp4",
        "--output_file", "animation.mp4",
        "--media_dir", str(base_path)
    ]

    subprocess.run(command, check=True)
    return str(output_path)
