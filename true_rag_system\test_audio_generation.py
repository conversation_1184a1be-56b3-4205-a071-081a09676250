"""
Test audio generation to verify T<PERSON> is working
"""

import os
import sys
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.synchronized_tts import SynchronizedTTS

def test_audio_generation():
    """Test if audio is being generated correctly"""
    
    print("🎵 Testing Audio Generation")
    print("=" * 50)
    
    # Initialize TTS
    tts = SynchronizedTTS()
    
    # Test script
    test_script = "Hello, this is a test of the audio generation system. Can you hear this clearly?"
    
    print(f"📝 Test script: {test_script}")
    
    try:
        # Generate audio
        print("🎵 Generating audio...")
        audio_file = tts.generate_timed_audio(test_script, "audio_test")
        
        if audio_file and Path(audio_file).exists():
            print(f"✅ Audio generated: {audio_file}")
            
            # Check file size
            file_size = Path(audio_file).stat().st_size
            print(f"📊 File size: {file_size} bytes")
            
            if file_size > 1000:  # Should be more than 1KB for actual audio
                print("✅ File size looks good - likely contains audio")
            else:
                print("⚠️ File size is very small - might be empty")
            
            # Try to get audio info using pydub
            try:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(audio_file)
                duration = len(audio) / 1000.0  # Convert to seconds
                print(f"⏱️ Audio duration: {duration:.2f} seconds")
                print(f"🔊 Audio channels: {audio.channels}")
                print(f"📊 Sample rate: {audio.frame_rate} Hz")
                
                if duration > 0:
                    print("✅ Audio contains actual content")
                else:
                    print("❌ Audio appears to be empty")
                    
            except Exception as e:
                print(f"⚠️ Could not analyze audio: {e}")
            
            return audio_file
        else:
            print("❌ Audio file not generated")
            return None
            
    except Exception as e:
        print(f"❌ Audio generation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_existing_audio():
    """Test existing audio files from True RAG generation"""
    
    print("\n🔍 Testing Existing Audio Files")
    print("=" * 50)
    
    # Check existing audio files
    output_dir = Path("true_rag_output")
    
    if not output_dir.exists():
        print("❌ No output directory found")
        return
    
    audio_files = list(output_dir.glob("*/audio.mp3"))
    
    if not audio_files:
        print("❌ No audio files found")
        return
    
    print(f"📁 Found {len(audio_files)} audio files")
    
    for audio_file in audio_files[:3]:  # Test first 3
        print(f"\n🎵 Testing: {audio_file}")
        
        try:
            # Check file size
            file_size = audio_file.stat().st_size
            print(f"📊 File size: {file_size} bytes")
            
            # Analyze with pydub
            from pydub import AudioSegment
            audio = AudioSegment.from_file(audio_file)
            duration = len(audio) / 1000.0
            
            print(f"⏱️ Duration: {duration:.2f} seconds")
            print(f"🔊 Channels: {audio.channels}")
            print(f"📊 Sample rate: {audio.frame_rate} Hz")
            
            # Check if audio has content (not silent)
            if audio.max_possible_amplitude > 0:
                max_amplitude = audio.max
                print(f"🔊 Max amplitude: {max_amplitude}")
                
                if max_amplitude > 100:  # Arbitrary threshold
                    print("✅ Audio contains sound")
                else:
                    print("⚠️ Audio might be very quiet or silent")
            else:
                print("❌ Audio appears to be silent")
                
        except Exception as e:
            print(f"❌ Error analyzing {audio_file}: {e}")

def test_video_audio_merge():
    """Test if videos actually have audio tracks"""
    
    print("\n🎬 Testing Video Audio Tracks")
    print("=" * 50)
    
    output_dir = Path("true_rag_output")
    video_files = list(output_dir.glob("*/final_video.mp4"))
    
    if not video_files:
        print("❌ No video files found")
        return
    
    print(f"📹 Found {len(video_files)} video files")
    
    for video_file in video_files[:2]:  # Test first 2
        print(f"\n🎬 Testing: {video_file}")
        
        try:
            # Use ffprobe to check audio tracks
            import subprocess
            
            # Try to find ffprobe
            ffprobe_cmd = "ffprobe"
            
            command = [
                ffprobe_cmd,
                "-v", "quiet",
                "-print_format", "json",
                "-show_streams",
                str(video_file)
            ]
            
            result = subprocess.run(command, capture_output=True, text=True)
            
            if result.returncode == 0:
                import json
                data = json.loads(result.stdout)
                
                video_streams = [s for s in data['streams'] if s['codec_type'] == 'video']
                audio_streams = [s for s in data['streams'] if s['codec_type'] == 'audio']
                
                print(f"📹 Video streams: {len(video_streams)}")
                print(f"🔊 Audio streams: {len(audio_streams)}")
                
                if audio_streams:
                    for i, stream in enumerate(audio_streams):
                        print(f"   Audio {i}: {stream.get('codec_name', 'unknown')} codec")
                        duration = stream.get('duration', 'unknown')
                        print(f"   Duration: {duration}")
                    print("✅ Video has audio track(s)")
                else:
                    print("❌ Video has NO audio tracks")
            else:
                print(f"⚠️ Could not analyze video: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error analyzing video: {e}")

def main():
    """Main test function"""
    
    print("🧪 Audio Generation and Integration Test")
    print("=" * 70)
    
    # Test 1: Generate new audio
    test_audio_generation()
    
    # Test 2: Check existing audio files
    test_existing_audio()
    
    # Test 3: Check video audio tracks
    test_video_audio_merge()
    
    print("\n🎯 Audio Test Summary")
    print("=" * 50)
    print("This test helps identify where the audio issue is:")
    print("1. TTS generation problem")
    print("2. Audio file corruption")
    print("3. Video merge problem")
    print("4. Audio track missing in final video")

if __name__ == "__main__":
    main()
