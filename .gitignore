# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
.venv/
venv/
ENV/
env/
.env

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project Specific
# Generated content (videos, audio, images)
generated/
media/

# Large data directories (excluded for now due to GitHub size limits)
3b1b_data/
manim_rag_db/

# API Keys and sensitive config
.env
*.key

# Temporary files
*.tmp
*.temp
*.log

# Manim cache
.manim_cache/

# Audio/Video files
*.mp4
*.mp3
*.wav
*.avi
*.mov
*.gif

# Large model files (except RAG database files)
*.bin
!manim_rag_db/**/*.bin
*.safetensors
models/

# Database files (except RAG database)
*.db
!manim_rag_db/*.sqlite3
*.sqlite3
!manim_rag_db/chroma.sqlite3
*.sqlite

# Backup files
*.bak
*.backup

# React/Node.js
frontend_react/node_modules/
frontend_react/build/
frontend_react/.env.local
frontend_react/.env.development.local
frontend_react/.env.test.local
frontend_react/.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Test downloads and temporary files
downloads_*/
test_*.mp4
test_*.mp3

# API server logs and temporary files
uvicorn.log
*.pid

# Generated content directories (keep structure but ignore content)
generated/*/
!generated/.gitkeep

# Custom config files (may contain sensitive data)
custom_config.yml
config.yml

# Downloaded videos and test files
downloaded_video.mp4
*.mfput.log
mfput.log
