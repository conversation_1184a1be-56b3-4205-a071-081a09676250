#!/usr/bin/env python3
"""
FastAPI server for RAG-Enhanced 3Blue1Brown Educational Video Generator
Provides REST API endpoints for generating educational videos
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, Dict, Any
import os
import uuid
import asyncio
import json
from pathlib import Path
import logging
from datetime import datetime

# Import our existing modules
from app.llm_rag_audio_first import AudioFirstRAGEnhancedManimLLM
from app.manim_3b1b_runner import generate_video_3b1b
from app.video_merger import merge_audio_video

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="Audio-First RAG-Enhanced 3Blue1Brown Video Generator API",
    description="Generate educational videos with natural speech pacing using AI and RAG",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Serve static files (generated videos)
app.mount("/static", StaticFiles(directory="generated"), name="static")

# Global variables for system components
rag_system = None
audio_first_llm = None

# Request/Response models
class VideoRequest(BaseModel):
    topic: str
    output_format: Optional[str] = "mp4"  # "mp4" or "gif"
    job_id: Optional[str] = None
    custom_script: Optional[str] = None  # User-provided script (optional)

class VideoResponse(BaseModel):
    job_id: str
    status: str
    message: str
    topic: str
    output_format: str
    created_at: str

class JobStatus(BaseModel):
    job_id: str
    status: str  # "pending", "processing", "completed", "failed"
    progress: str
    topic: str
    output_format: str
    created_at: str
    completed_at: Optional[str] = None
    files: Optional[Dict[str, str]] = None
    error: Optional[str] = None

# In-memory job tracking (in production, use Redis or database)
jobs: Dict[str, JobStatus] = {}

@app.on_event("startup")
async def startup_event():
    """Initialize RAG system and Audio-First LLM on startup"""
    global rag_system, audio_first_llm

    logger.info("🚀 Starting Audio-First RAG-Enhanced 3Blue1Brown API Server...")

    try:
        # Initialize Audio-First LLM (includes RAG initialization)
        logger.info("🎵 Initializing Audio-First RAG-Enhanced LLM...")
        audio_first_llm = AudioFirstRAGEnhancedManimLLM()

        # Get the RAG system reference for health checks
        rag_system = audio_first_llm.rag

        if rag_system and rag_system.collection.count() == 0:
            logger.warning("⚠️ RAG database is empty. Please run setup_3b1b_rag.py first")
            raise Exception("RAG database not populated")

        logger.info("✅ Audio-First API Server ready!")
        logger.info("🎵 Natural speech pacing enabled")
        logger.info("📚 RAG enhancement available")
        logger.info("🎬 3Blue1Brown manim integration ready")

    except Exception as e:
        logger.error(f"❌ Failed to initialize system: {e}")
        raise e

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Audio-First RAG-Enhanced 3Blue1Brown Video Generator API",
        "version": "1.0.0",
        "features": ["Natural speech pacing", "RAG enhancement", "3Blue1Brown style"],
        "frontend": "React app available at http://localhost:3000",
        "docs": "/docs",
        "endpoints": {
            "generate": "/generate",
            "status": "/status/{job_id}",
            "download": "/download/{job_id}/{file_type}",
            "health": "/health"
        }
    }

@app.get("/api")
async def api_info():
    """API information endpoint"""
    return {
        "message": "Audio-First RAG-Enhanced 3Blue1Brown Video Generator API",
        "version": "1.0.0",
        "features": ["Natural speech pacing", "RAG enhancement", "3Blue1Brown style"],
        "docs": "/docs",
        "endpoints": {
            "generate": "/generate",
            "status": "/status/{job_id}",
            "download": "/download/{job_id}/{file_type}",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    global rag_system, audio_first_llm

    # Check database status
    database_ok = False
    if rag_system:
        try:
            collection = rag_system.client.get_collection(rag_system.collection_name)
            database_ok = collection.count() > 0
        except Exception:
            database_ok = False

    status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "components": {
            "rag_system": rag_system is not None,
            "audio_first_llm": audio_first_llm is not None,
            "tts_system": audio_first_llm.tts is not None if audio_first_llm else False,
            "database": database_ok
        }
    }

    if not all(status["components"].values()):
        status["status"] = "unhealthy"
        raise HTTPException(status_code=503, detail=status)

    return status

@app.post("/generate", response_model=VideoResponse)
async def generate_video(request: VideoRequest, background_tasks: BackgroundTasks):
    """Generate educational video with audio-first approach"""
    global rag_system, audio_first_llm

    if not audio_first_llm:
        raise HTTPException(status_code=503, detail="Audio-first system not initialized")
    
    # Generate job ID
    job_id = request.job_id or str(uuid.uuid4())[:8]
    
    # Validate output format
    if request.output_format not in ["mp4", "gif"]:
        raise HTTPException(status_code=400, detail="output_format must be 'mp4' or 'gif'")
    
    # Create job status
    job_status = JobStatus(
        job_id=job_id,
        status="pending",
        progress="Job queued for processing",
        topic=request.topic,
        output_format=request.output_format,
        created_at=datetime.now().isoformat()
    )
    
    jobs[job_id] = job_status
    
    # Start background task
    background_tasks.add_task(
        process_video_generation,
        job_id,
        request.topic,
        request.output_format,
        request.custom_script
    )
    
    return VideoResponse(
        job_id=job_id,
        status="accepted",
        message="Video generation started",
        topic=request.topic,
        output_format=request.output_format,
        created_at=job_status.created_at
    )

@app.get("/status/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    """Get status of video generation job"""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return jobs[job_id]

@app.get("/download/{job_id}/{file_type}")
async def download_file(job_id: str, file_type: str):
    """Download generated files"""
    if job_id not in jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = jobs[job_id]
    if job.status != "completed":
        raise HTTPException(status_code=400, detail="Job not completed")
    
    if not job.files or file_type not in job.files:
        raise HTTPException(status_code=404, detail="File not found")
    
    file_path = Path(job.files[file_type])
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found on disk")
    
    return FileResponse(
        path=str(file_path),
        filename=file_path.name,
        media_type='application/octet-stream'
    )

@app.get("/jobs")
async def list_jobs():
    """List all jobs"""
    return {"jobs": list(jobs.values())}

async def process_video_generation(job_id: str, topic: str, output_format: str, custom_script: str = None):
    """Background task to process audio-first video generation"""
    global rag_system, audio_first_llm

    try:
        # Update status
        jobs[job_id].status = "processing"
        jobs[job_id].progress = "Starting audio-first generation..."

        logger.info(f"🎵 Processing audio-first job {job_id}: {topic}")

        # Step 1: Generate audio-first content (script, audio, and synchronized code)
        if custom_script:
            jobs[job_id].progress = "Using custom script and generating audio..."
            logger.info(f"🔍 DEBUG: Using custom script (first 100 chars): {custom_script[:100]}...")
        else:
            jobs[job_id].progress = "Generating script with natural speech pacing..."
            logger.info(f"🔍 DEBUG: No custom script provided, will auto-generate")
        result = audio_first_llm.generate_audio_first_content(topic, job_id, custom_script)

        jobs[job_id].progress = f"Audio generated: {result['natural_duration']:.1f}s, creating video..."

        # Step 2: Generate video with the synchronized manim code
        video_path = None

        # Check if compilation testing was successful
        compilation_successful = result.get('compilation_successful', False)

        if not compilation_successful:
            logger.warning("⚠️ Skipping video generation - compilation testing failed")
            video_path = None
        else:
            try:
                video_path = generate_video_3b1b(
                    result['manim_code'],
                    job_id,
                    output_format=output_format
                )
                logger.info(f"✅ Video generated: {video_path}")
            except Exception as e:
                logger.error(f"❌ Video generation failed: {e}")
                # Continue without video - we still have audio
                video_path = None

        # Audio is already generated in step 1
        audio_path = result['audio_path']

        # Step 3: Merge video and audio (only if video generation succeeded)
        final_video_path = None
        if video_path and Path(video_path).exists():
            jobs[job_id].progress = "Video generated, merging with audio..."
            try:
                final_video_path = merge_audio_video(
                    str(audio_path),
                    str(video_path),
                    job_id,
                    "complete_video"
                )
                logger.info(f"✅ Video and audio merged: {final_video_path}")
            except Exception as e:
                logger.warning(f"⚠️ Video merge failed, keeping separate files: {e}")
                final_video_path = None
        else:
            logger.warning("⚠️ Skipping video merge - no video file available")

        # Step 3: Save files and update status
        output_dir = Path("generated") / job_id

        files = {
            "audio": str(audio_path),
            "script": str(output_dir / "script.txt"),
            "code": str(output_dir / "manim_code.py")
        }

        # Add video if generation succeeded
        if video_path:
            files["video"] = str(video_path)

        # Add merged video if successful
        if final_video_path:
            files["complete_video"] = str(final_video_path)

        # Add audio-first specific files
        generation_info_path = output_dir / "generation_info.json"
        if generation_info_path.exists():
            files["generation_info"] = str(generation_info_path)

        # Add RAG examples if available
        rag_examples_path = output_dir / "rag_examples.txt"
        if rag_examples_path.exists():
            files["rag_examples"] = str(rag_examples_path)

        jobs[job_id].status = "completed"
        jobs[job_id].progress = f"Audio-first generation completed: {result['natural_duration']:.1f}s duration"
        jobs[job_id].completed_at = datetime.now().isoformat()
        jobs[job_id].files = files

        # Add audio-first specific metadata
        if hasattr(jobs[job_id], '__dict__'):
            jobs[job_id].__dict__.update({
                "natural_duration": result.get('natural_duration', 0),
                "audio_segments": len(result.get('segments', [])),
                "rag_enhanced": result.get('rag_enhanced', False)
            })

        logger.info(f"✅ Audio-first job {job_id} completed successfully")
        logger.info(f"📊 Duration: {result['natural_duration']:.1f}s, Segments: {len(result.get('segments', []))}")
        logger.info(f"📚 RAG Enhanced: {result.get('rag_enhanced', False)}")
        
    except Exception as e:
        logger.error(f"❌ Job {job_id} failed: {e}")
        jobs[job_id].status = "failed"
        jobs[job_id].progress = "Generation failed"
        jobs[job_id].error = str(e)
        jobs[job_id].completed_at = datetime.now().isoformat()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
