"""
Audio-First 3Blue1Brown Video Generator
Natural speech pace with synchronized video animations
"""

import os
import json
from pathlib import Path
from dotenv import load_dotenv

# Import our audio-first modules
from app.audio_first_generator import generate_audio_first_video
from app.manim_3b1b_runner import generate_video_3b1b, check_3b1b_manim_installation
from app.video_merger import merge_audio_video

load_dotenv()


def generate_audio_first_3b1b_video(prompt: str, job_id: str = None, 
                                   output_format: str = "mp4") -> dict:
    """
    Generate a 3Blue1Brown-style video with natural audio pacing
    
    Args:
        prompt: Educational topic to visualize
        job_id: Unique identifier (auto-generated if None)
        output_format: Output format ("mp4" or "gif")
    
    Returns:
        Dictionary with paths to generated files and metadata
    """
    
    # Generate job ID if not provided
    if job_id is None:
        import uuid
        job_id = str(uuid.uuid4())[:8]
    
    print("🎬 Audio-First 3Blue1Brown Video Generator")
    print("=" * 60)
    print(f"📝 Topic: {prompt}")
    print(f"🆔 Job ID: {job_id}")
    print(f"📹 Format: {output_format.upper()}")
    print(f"🎯 Method: Natural audio pace with synchronized video")
    print()
    
    # Check 3b1b manim installation
    print("🔍 Checking 3Blue1Brown manim installation...")
    if not check_3b1b_manim_installation():
        print("❌ 3Blue1Brown manim not found. Please run setup_3b1b_rag.py first.")
        return {"success": False, "error": "3b1b manim not installed"}
    print("✅ 3Blue1Brown manim found")
    print()
    
    results = {
        "job_id": job_id,
        "prompt": prompt,
        "method": "audio_first",
        "success": False,
        "files": {},
        "timing": {}
    }
    
    try:
        # Step 1: Generate natural script and audio, then synchronized video code
        print("🎯 Step 1: Audio-First Content Generation")
        print("-" * 50)
        
        content_result = generate_audio_first_video(prompt, job_id)
        
        natural_duration = content_result["natural_duration"]
        print(f"✅ Audio-first generation completed!")
        print(f"📊 Natural audio duration: {natural_duration:.2f} seconds")
        print(f"🎵 Audio file: {content_result['audio_path']}")
        print()
        
        results["timing"]["natural_duration"] = natural_duration
        results["files"]["script"] = f"generated/{job_id}/script.txt"
        results["files"]["manim_code"] = f"generated/{job_id}/manim_code.py"
        results["files"]["audio"] = content_result["audio_path"]
        
        # Step 2: Generate video with synchronized timing
        print(f"🎬 Step 2: Generating {output_format.upper()} with synchronized timing")
        print("-" * 50)
        print(f"🎯 Target video duration: {natural_duration:.2f} seconds (matching audio)")
        
        video_path = generate_video_3b1b(
            content_result["manim_code"], 
            job_id, 
            output_format=output_format
        )
        
        if video_path:
            print(f"✅ Video generated: {video_path}")
            results["files"]["video"] = video_path
        else:
            print("❌ Video generation failed")
            return results
        print()
        
        # Step 3: Merge audio and video
        print("🔗 Step 3: Merging natural audio with synchronized video")
        print("-" * 50)
        
        try:
            final_video_path = merge_audio_video(
                content_result["audio_path"], 
                video_path, 
                job_id,
                output_name="audio_first_video"
            )
            
            print(f"✅ Final synchronized video created: {final_video_path}")
            results["files"]["final_video"] = final_video_path
            results["success"] = True
            
        except Exception as e:
            print(f"⚠️ Video merging failed: {e}")
            print("📁 Separate audio and video files are available")
            results["success"] = True  # Still success since we have both files
        
        print()
        
        # Step 4: Generate summary report
        print("📋 Step 4: Generating summary report")
        print("-" * 50)
        
        summary = generate_audio_first_summary(results, content_result)
        summary_path = f"generated/{job_id}/audio_first_summary.json"
        
        with open(summary_path, "w", encoding="utf-8") as f:
            json.dump(summary, f, indent=2)
        
        results["files"]["summary"] = summary_path
        print(f"✅ Summary report saved: {summary_path}")
        print()
        
        # Final summary
        print("🎉 AUDIO-FIRST GENERATION COMPLETE!")
        print("=" * 60)
        print(f"📁 All files saved in: generated/{job_id}/")
        print(f"🎬 Final video: {results['files'].get('final_video', 'Not merged')}")
        print(f"🎵 Natural audio: {results['files'].get('audio', 'Not generated')}")
        print(f"📹 Synchronized video: {results['files'].get('video', 'Not generated')}")
        print(f"📝 Script: {results['files'].get('script', 'Not generated')}")
        print(f"🐍 Code: {results['files'].get('manim_code', 'Not generated')}")
        print()
        print("🎯 Key Benefits:")
        print(f"  ✅ Natural speech pace ({natural_duration:.1f}s duration)")
        print("  ✅ Perfect audio-video synchronization")
        print("  ✅ Educational content optimized for learning")
        print("  ✅ 3Blue1Brown style animations")
        
        return results
        
    except Exception as e:
        print(f"❌ Generation failed: {e}")
        results["error"] = str(e)
        return results


def generate_audio_first_summary(results: dict, content_result: dict) -> dict:
    """Generate a comprehensive summary report for audio-first generation"""
    
    summary = {
        "generation_info": {
            "job_id": results["job_id"],
            "prompt": results["prompt"],
            "method": "audio_first",
            "natural_duration": content_result["natural_duration"],
            "success": results["success"],
            "timestamp": str(Path(f"generated/{results['job_id']}").stat().st_mtime)
        },
        "audio_analysis": {
            "natural_duration": content_result["natural_duration"],
            "audio_timing": content_result["audio_timing"],
            "speech_pace": "natural",
            "compression_applied": False
        },
        "synchronization": {
            "method": "video_synchronized_to_audio",
            "target_duration": content_result["natural_duration"],
            "timing_source": "natural_speech"
        },
        "files_generated": results["files"],
        "advantages": [
            "Natural speaking pace",
            "No audio compression artifacts", 
            "Perfect timing synchronization",
            "Educational content optimized",
            "3Blue1Brown style animations"
        ]
    }
    
    # Add file size information
    for file_type, file_path in results["files"].items():
        if file_path and Path(file_path).exists():
            file_size = Path(file_path).stat().st_size
            summary["files_generated"][f"{file_type}_size_mb"] = round(file_size / (1024 * 1024), 2)
    
    return summary


def main():
    """Interactive main function"""
    
    print("🎬 Audio-First 3Blue1Brown Video Generator")
    print("=" * 60)
    print("🎯 Natural speech pace with synchronized video animations")
    print()
    
    # Get user input
    prompt = input("Enter your mathematical topic: ").strip()
    if not prompt:
        prompt = "the concept of derivatives and rates of change"
        print(f"Using default topic: {prompt}")
    
    # Get output format
    format_input = input("Output format (mp4/gif, default: mp4): ").strip().lower()
    output_format = format_input if format_input in ["mp4", "gif"] else "mp4"
    
    print()
    print("🎯 Audio-First Approach:")
    print("  1. Generate natural educational script")
    print("  2. Create audio at comfortable speaking pace")
    print("  3. Generate video synchronized to audio timing")
    print("  4. Merge for final synchronized output")
    print()
    
    # Generate video
    results = generate_audio_first_3b1b_video(
        prompt=prompt,
        output_format=output_format
    )
    
    if results["success"]:
        print("\n🎯 Next Steps:")
        print("1. Review the natural-paced video")
        print("2. Check the audio-video synchronization")
        print("3. Compare with previous compressed audio versions")
        print("4. Iterate and improve based on results")
        
        # Show comparison with previous method
        natural_duration = results["timing"].get("natural_duration", 0)
        print(f"\n📊 Audio-First Results:")
        print(f"  🎵 Natural duration: {natural_duration:.1f} seconds")
        print(f"  🗣️ Speech pace: Natural and comfortable")
        print(f"  🎬 Video: Synchronized to match audio timing")
        print(f"  ✅ No audio compression artifacts")
        
    else:
        print(f"\n❌ Generation failed: {results.get('error', 'Unknown error')}")


if __name__ == "__main__":
    main()
