"""
Setup script for True RAG Enhanced Manim System

This script sets up the comprehensive True RAG system with:
1. Multi-source knowledge bases
2. Enhanced 3Blue1Brown manim examples
3. Mathematical concept relationships
4. Animation patterns and timing strategies
5. Error prevention knowledge
"""

import os
import sys
import json
from pathlib import Path

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent / "app"))

from app.true_rag_system import TrueRAGSystem

def setup_enhanced_manim_examples():
    """Setup enhanced manim examples with comprehensive metadata"""
    
    examples = [
        {
            'code': '''
from manimlib import *

class CalculusIntroduction(Scene):
    def construct(self):
        # Title
        title = Text("What is Calculus?", font_size=72)
        self.play(Write(title))
        self.wait(1)
        self.play(FadeOut(title))
        
        # Setup axes
        axes = Axes(x_range=(-3, 3, 1), y_range=(-2, 8, 1))
        self.play(ShowCreation(axes))
        
        # Function
        graph = axes.get_graph(lambda x: x**2, color=BLUE)
        func_label = Text("f(x) = x²", font_size=48).next_to(graph, UP)
        
        self.play(ShowCreation(graph))
        self.play(Write(func_label))
        self.wait(2)
        
        # Derivative concept
        x_val = 2
        tangent = axes.get_tangent_line(graph, x_val, length=3, color=RED)
        tangent_label = Text("Tangent Line", font_size=36, color=RED).next_to(tangent, UP)
        
        self.play(ShowCreation(tangent))
        self.play(Write(tangent_label))
        self.wait(2)
        
        # Cleanup
        self.play(FadeOut(axes), FadeOut(graph), FadeOut(func_label), 
                 FadeOut(tangent), FadeOut(tangent_label))
''',
            'metadata': {
                'title': 'Calculus Introduction',
                'concepts': ['calculus', 'derivative', 'function', 'tangent_line'],
                'complexity': 'intermediate',
                'duration': 15,
                'educational_level': 'high_school_advanced',
                'objects_used': ['Text', 'Axes', 'ParametricFunction', 'Line'],
                'animation_types': ['Write', 'ShowCreation', 'FadeOut'],
                'mathematical_depth': 'conceptual_introduction',
                'visual_elements': ['coordinate_system', 'function_graph', 'tangent_visualization'],
                'timing_structure': 'intro_build_explain_conclude',
                'prerequisite_concepts': ['function', 'coordinate_system', 'slope'],
                'learning_objectives': ['understand_derivative_concept', 'visualize_tangent_relationship'],
                'common_errors_addressed': ['derivative_vs_function_confusion'],
                'style_tags': ['3blue1brown', 'intuitive', 'visual']
            }
        },
        {
            'code': '''
from manimlib import *

class VectorSpaceVisualization(Scene):
    def construct(self):
        # Title
        title = Text("Vector Addition", font_size=72)
        self.play(Write(title))
        self.wait(1)
        self.play(FadeOut(title))
        
        # Create coordinate system
        axes = Axes(x_range=(-4, 4, 1), y_range=(-3, 3, 1))
        self.play(ShowCreation(axes))
        
        # First vector
        v1 = Arrow(ORIGIN, [2, 1, 0], color=RED, buff=0)
        v1_label = Text("v₁", font_size=36, color=RED).next_to(v1.get_end(), RIGHT)
        
        self.play(ShowCreation(v1))
        self.play(Write(v1_label))
        self.wait(1)
        
        # Second vector
        v2 = Arrow(ORIGIN, [1, 2, 0], color=BLUE, buff=0)
        v2_label = Text("v₂", font_size=36, color=BLUE).next_to(v2.get_end(), UP)
        
        self.play(ShowCreation(v2))
        self.play(Write(v2_label))
        self.wait(1)
        
        # Vector addition - move second vector to tip of first
        v2_moved = Arrow([2, 1, 0], [3, 3, 0], color=BLUE, buff=0)
        v2_moved_label = Text("v₂", font_size=36, color=BLUE).next_to(v2_moved.get_end(), UP)
        
        self.play(Transform(v2, v2_moved), Transform(v2_label, v2_moved_label))
        self.wait(1)
        
        # Result vector
        result = Arrow(ORIGIN, [3, 3, 0], color=GREEN, buff=0)
        result_label = Text("v₁ + v₂", font_size=36, color=GREEN).next_to(result.get_end(), RIGHT)
        
        self.play(ShowCreation(result))
        self.play(Write(result_label))
        self.wait(2)
        
        # Cleanup
        self.play(FadeOut(axes), FadeOut(v1), FadeOut(v1_label),
                 FadeOut(v2), FadeOut(v2_label), FadeOut(result), FadeOut(result_label))
''',
            'metadata': {
                'title': 'Vector Space Visualization',
                'concepts': ['vector', 'vector_addition', 'linear_algebra', 'coordinate_system'],
                'complexity': 'basic_to_intermediate',
                'duration': 18,
                'educational_level': 'high_school_to_university',
                'objects_used': ['Text', 'Axes', 'Arrow', 'Transform'],
                'animation_types': ['Write', 'ShowCreation', 'Transform', 'FadeOut'],
                'mathematical_depth': 'foundational_concept',
                'visual_elements': ['coordinate_system', 'vector_arrows', 'vector_addition_visualization'],
                'timing_structure': 'intro_build_demonstrate_conclude',
                'prerequisite_concepts': ['coordinate_system', 'direction', 'magnitude'],
                'learning_objectives': ['understand_vector_addition', 'visualize_vector_operations'],
                'common_errors_addressed': ['vector_vs_scalar_confusion', 'wrong_addition_method'],
                'style_tags': ['3blue1brown', 'step_by_step', 'visual_proof']
            }
        },
        {
            'code': '''
from manimlib import *

class AreaUnderCurve(Scene):
    def construct(self):
        # Title
        title = Text("Area Under a Curve", font_size=72)
        self.play(Write(title))
        self.wait(1)
        self.play(FadeOut(title))
        
        # Setup
        axes = Axes(x_range=(0, 4, 1), y_range=(0, 5, 1))
        self.play(ShowCreation(axes))
        
        # Function
        graph = axes.get_graph(lambda x: 0.5 * x**2 + 1, color=BLUE)
        func_label = Text("f(x)", font_size=48, color=BLUE).next_to(graph, UP)
        
        self.play(ShowCreation(graph))
        self.play(Write(func_label))
        self.wait(1)
        
        # Riemann rectangles
        rectangles = axes.get_riemann_rectangles(graph, [1, 3], dx=0.5, input_sample_type="left")
        
        self.play(ShowCreation(rectangles))
        self.wait(2)
        
        # More rectangles (finer approximation)
        finer_rectangles = axes.get_riemann_rectangles(graph, [1, 3], dx=0.1, input_sample_type="left")
        
        self.play(Transform(rectangles, finer_rectangles))
        self.wait(2)
        
        # Show the actual area
        area = axes.get_area(graph, [1, 3], color=YELLOW, opacity=0.5)
        area_label = Text("∫f(x)dx", font_size=48, color=YELLOW).next_to(area, DOWN)
        
        self.play(Transform(rectangles, area))
        self.play(Write(area_label))
        self.wait(3)
        
        # Cleanup
        self.play(FadeOut(axes), FadeOut(graph), FadeOut(func_label),
                 FadeOut(rectangles), FadeOut(area_label))
''',
            'metadata': {
                'title': 'Area Under Curve Integration',
                'concepts': ['integral', 'area', 'riemann_sum', 'calculus', 'approximation'],
                'complexity': 'intermediate_to_advanced',
                'duration': 20,
                'educational_level': 'university_calculus',
                'objects_used': ['Text', 'Axes', 'ParametricFunction', 'Rectangle', 'Polygon'],
                'animation_types': ['Write', 'ShowCreation', 'Transform', 'FadeOut'],
                'mathematical_depth': 'conceptual_to_formal',
                'visual_elements': ['coordinate_system', 'function_graph', 'riemann_rectangles', 'area_visualization'],
                'timing_structure': 'intro_build_approximate_refine_conclude',
                'prerequisite_concepts': ['function', 'area', 'limit', 'approximation'],
                'learning_objectives': ['understand_integration_concept', 'visualize_riemann_sums', 'connect_area_to_integral'],
                'common_errors_addressed': ['integral_vs_derivative_confusion', 'riemann_sum_misconceptions'],
                'style_tags': ['3blue1brown', 'progressive_refinement', 'visual_proof']
            }
        }
    ]
    
    return examples

def setup_mathematical_concepts():
    """Setup comprehensive mathematical concept database"""
    
    concepts = [
        {
            'name': 'derivative',
            'data': {
                'category': 'calculus',
                'difficulty_level': 4,
                'prerequisites': ['function', 'limit', 'slope', 'tangent_line'],
                'visual_patterns': ['tangent_line_visualization', 'slope_field', 'rate_of_change_animation'],
                'common_errors': [
                    'confusing_derivative_with_integral',
                    'wrong_chain_rule_application',
                    'derivative_of_constant_confusion',
                    'product_rule_vs_chain_rule'
                ],
                'related_concepts': ['integral', 'limit', 'continuity', 'optimization'],
                'applications': ['physics_velocity', 'economics_marginal_cost', 'geometry_tangent_lines'],
                'visualization_strategies': ['dynamic_tangent_lines', 'secant_to_tangent_transition', 'derivative_graph_construction'],
                'pedagogical_notes': ['start_with_geometric_intuition', 'connect_to_rate_of_change', 'emphasize_local_linearity']
            }
        },
        {
            'name': 'integral',
            'data': {
                'category': 'calculus',
                'difficulty_level': 4,
                'prerequisites': ['function', 'area', 'riemann_sum', 'limit'],
                'visual_patterns': ['riemann_rectangle_approximation', 'area_under_curve', 'antiderivative_visualization'],
                'common_errors': [
                    'integral_vs_derivative_confusion',
                    'wrong_integration_bounds',
                    'forgetting_constant_of_integration',
                    'improper_substitution'
                ],
                'related_concepts': ['derivative', 'area', 'fundamental_theorem_of_calculus', 'antiderivative'],
                'applications': ['area_calculation', 'physics_displacement', 'probability_distributions'],
                'visualization_strategies': ['progressive_riemann_refinement', 'area_accumulation', 'fundamental_theorem_demonstration'],
                'pedagogical_notes': ['start_with_area_intuition', 'connect_to_antiderivative', 'emphasize_accumulation_concept']
            }
        },
        {
            'name': 'vector',
            'data': {
                'category': 'linear_algebra',
                'difficulty_level': 2,
                'prerequisites': ['coordinate_system', 'direction', 'magnitude'],
                'visual_patterns': ['arrow_representation', 'component_breakdown', 'vector_addition_parallelogram'],
                'common_errors': [
                    'vector_vs_scalar_confusion',
                    'wrong_vector_addition',
                    'magnitude_calculation_error',
                    'component_vs_vector_confusion'
                ],
                'related_concepts': ['matrix', 'linear_transformation', 'dot_product', 'cross_product'],
                'applications': ['physics_forces', 'computer_graphics', 'navigation'],
                'visualization_strategies': ['arrow_animations', 'component_highlighting', 'geometric_operations'],
                'pedagogical_notes': ['emphasize_direction_and_magnitude', 'use_physical_analogies', 'build_from_2D_to_3D']
            }
        },
        {
            'name': 'function',
            'data': {
                'category': 'algebra',
                'difficulty_level': 2,
                'prerequisites': ['variable', 'equation', 'coordinate_system'],
                'visual_patterns': ['input_output_mapping', 'graph_visualization', 'transformation_animation'],
                'common_errors': [
                    'function_vs_equation_confusion',
                    'domain_range_misconceptions',
                    'vertical_line_test_failure',
                    'composition_order_confusion'
                ],
                'related_concepts': ['domain', 'range', 'inverse_function', 'composition'],
                'applications': ['modeling_relationships', 'data_analysis', 'optimization'],
                'visualization_strategies': ['mapping_diagrams', 'graph_construction', 'transformation_sequences'],
                'pedagogical_notes': ['start_with_concrete_examples', 'emphasize_input_output_relationship', 'build_graphical_intuition']
            }
        }
    ]
    
    return concepts

def setup_animation_patterns():
    """Setup animation patterns and timing strategies"""
    
    patterns = {
        'timing_patterns': {
            'introduction_sequence': {
                'structure': 'hook -> title -> context_setup',
                'timing': [2, 3, 4],
                'total_duration': 9,
                'pacing': 'moderate_to_fast',
                'success_rate': 0.85
            },
            'concept_explanation': {
                'structure': 'visual_setup -> intuition_building -> formal_definition -> examples',
                'timing': [5, 8, 6, 10],
                'total_duration': 29,
                'pacing': 'slow_to_moderate',
                'success_rate': 0.92
            },
            'proof_demonstration': {
                'structure': 'setup -> key_insight -> step_by_step -> conclusion',
                'timing': [4, 6, 15, 5],
                'total_duration': 30,
                'pacing': 'deliberate',
                'success_rate': 0.78
            }
        },
        'animation_patterns': {
            'mathematical_object_introduction': {
                'sequence': ['ShowCreation', 'highlight', 'label', 'context'],
                'timing_ratios': [0.3, 0.2, 0.3, 0.2],
                'effectiveness': 0.88
            },
            'transformation_demonstration': {
                'sequence': ['setup', 'Transform', 'pause', 'explanation'],
                'timing_ratios': [0.25, 0.4, 0.15, 0.2],
                'effectiveness': 0.91
            },
            'concept_building': {
                'sequence': ['simple_case', 'generalization', 'application', 'synthesis'],
                'timing_ratios': [0.3, 0.3, 0.25, 0.15],
                'effectiveness': 0.86
            }
        }
    }
    
    return patterns

def setup_error_prevention_knowledge():
    """Setup error prevention and solution knowledge"""
    
    error_knowledge = {
        'error_solutions': {
            'tex_usage_error': {
                'error_pattern': 'Tex() not supported',
                'solution': 'Replace with Text()',
                'prevention': 'Always use Text() for text rendering in 3Blue1Brown manim',
                'examples': [
                    {'wrong': 'Tex("$x^2$")', 'correct': 'Text("x²", font_size=48)'}
                ]
            },
            'get_tangent_line_error': {
                'error_pattern': 'get_tangent_line parameter error',
                'solution': 'Use correct parameter order: get_tangent_line(graph, x_value, length=3)',
                'prevention': 'Always pass x_value as second parameter, not as keyword argument',
                'examples': [
                    {'wrong': 'get_tangent_line(graph, x=2)', 'correct': 'get_tangent_line(graph, 2, length=3)'}
                ]
            },
            'vector_dimension_error': {
                'error_pattern': '2D vector in 3D space',
                'solution': 'Use 3D vectors: [x, y, 0] instead of [x, y]',
                'prevention': 'Always specify z-component in manim vectors',
                'examples': [
                    {'wrong': 'shift([2, 1])', 'correct': 'shift([2, 1, 0])'}
                ]
            }
        },
        'prevention_strategies': {
            'validation_before_execution': 'Always validate code syntax and manim compatibility',
            'error_aware_generation': 'Use error patterns to guide code generation',
            'iterative_correction': 'Fix specific errors rather than regenerating entire code'
        }
    }
    
    return error_knowledge

def main():
    """Main setup function for True RAG system"""
    
    print("🚀 Setting up True RAG Enhanced Manim System")
    print("=" * 60)
    
    # Initialize the True RAG system
    rag_system = TrueRAGSystem("true_rag_db")
    rag_system.initialize_knowledge_bases()
    
    print("\n📚 Adding enhanced manim examples...")
    examples = setup_enhanced_manim_examples()
    for example in examples:
        rag_system.add_manim_example(example['code'], example['metadata'])
    print(f"✅ Added {len(examples)} enhanced manim examples")
    
    print("\n🧮 Adding mathematical concepts...")
    concepts = setup_mathematical_concepts()
    for concept in concepts:
        rag_system.add_mathematical_concept(concept['name'], concept['data'])
    print(f"✅ Added {len(concepts)} mathematical concepts")
    
    print("\n🎬 Setting up animation patterns...")
    patterns = setup_animation_patterns()
    rag_system.pattern_db.update(patterns)
    rag_system._save_pattern_db()
    print(f"✅ Added animation patterns and timing strategies")
    
    print("\n🛡️ Setting up error prevention knowledge...")
    error_knowledge = setup_error_prevention_knowledge()
    rag_system.solution_db.update(error_knowledge)
    rag_system._save_solution_db()
    print(f"✅ Added error prevention and solution knowledge")
    
    print("\n🎉 True RAG system setup completed successfully!")
    print("The system now includes:")
    print("• Enhanced manim code examples with comprehensive metadata")
    print("• Mathematical concept relationships and prerequisites")
    print("• Animation patterns and timing strategies")
    print("• Error prevention and solution knowledge")
    print("• Multi-source knowledge integration capabilities")
    print("\nYou can now run 'python main_true_rag.py' to test the system!")

if __name__ == "__main__":
    main()
