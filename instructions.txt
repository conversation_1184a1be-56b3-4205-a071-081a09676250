# RAG-Enhanced 3Blue1Brown Educational Video Generator

## Overview
This system generates educational videos in 3Blue1Brown's style using <PERSON>'s actual codebase as reference. It combines RAG (Retrieval-Augmented Generation) with 3Blue1Brown's original manim library to create authentic mathematical animations with synchronized narration.

## Features
- 🤖 RAG system with 46,339+ code examples from 3Blue1Brown's repositories
- 📝 Educational script generation in 3b1b's teaching style
- 🎬 Authentic manim animations using <PERSON>'s original library
- 🔊 Text-to-speech narration with natural voice
- 🎥 High-quality MP4 video output (1080p, default)
- 🎞️ GIF animation support (when specified)
- ✅ Code validation system prevents syntax errors
- 🔄 Automatic retry with error feedback

## Prerequisites
- Python 3.11+ with virtual environment
- FFmpeg installed (configured automatically)
- OpenAI API key for LLM generation
- 3Blue1Brown's manim library (included in setup)

## Quick Start

### 1. Initial Setup (One-time)
```bash
# Activate your virtual environment
.venv\Scripts\activate

# Run the complete setup (downloads 3b1b repos and builds RAG database)
python setup_3b1b_rag.py
```

This will:
- Download 3Blue1Brown's manim library and video code repositories
- Extract and process all Python files
- Build a vector database with 46,339+ code examples
- Configure the RAG system for semantic search

**Note:** Initial setup takes 15-20 minutes and requires ~2GB of disk space.

### 2. Generate Educational Videos

#### Basic Usage:
```bash
# Activate virtual environment
.venv\Scripts\activate

# Generate a video on any mathematical topic (interactive mode)
python main_rag_3b1b.py

# Generate with specific topic (MP4 by default)
python main_rag_3b1b.py "quadratic functions"
```

The system will generate:
- Educational script
- 3Blue1Brown-style manim code
- Synchronized audio narration
- High-quality MP4 video (default) or GIF

#### Advanced Usage:
```bash
# Check RAG system status
python main_rag_3b1b.py check

# Generate MP4 video (default format)
python main_rag_3b1b.py "derivatives and limits" --mp4

# Generate GIF animation
python main_rag_3b1b.py "circle transformations" --gif

# Test RAG system independently
python main_rag_3b1b.py test-rag
```

## System Components

### Core Files:
- `main_rag_3b1b.py` - Main entry point for video generation
- `setup_3b1b_rag.py` - One-time setup script
- `app/rag_system.py` - RAG database and search functionality
- `app/llm_rag_enhanced.py` - LLM with RAG integration
- `app/tts.py` - Text-to-speech generation
- `custom_config.yml` - 3Blue1Brown manim configuration

### Generated Output Structure:
```
generated/
└── [job_id]/
    ├── script.txt              # Educational script
    ├── manim_code.py           # 3Blue1Brown manim code
    ├── voiceover.mp3           # TTS narration
    ├── rag_examples.txt        # Retrieved 3b1b examples
    └── [SceneName].mp4         # Final video
```

## Configuration

### Environment Variables:
Create a `.env` file with:
```
OPENAI_API_KEY=your_openai_api_key_here
```

### FFmpeg Configuration:
The system automatically configures FFmpeg using the path:
```
C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe
```

If your FFmpeg is installed elsewhere, update the path in:
- `app/tts.py` (lines 8-15)
- `custom_config.yml` (line 3)

## Troubleshooting

### Common Issues:

1. **"RAG database not found"**
   - Run `python setup_3b1b_rag.py` to build the database
   - Ensure the setup completed successfully

2. **"FFmpeg not found"**
   - Verify FFmpeg installation
   - Update paths in `app/tts.py` and `custom_config.yml`

3. **"OpenAI API error"**
   - Check your API key in `.env` file
   - Ensure you have sufficient API credits

4. **"Manim import errors"**
   - Ensure virtual environment is activated
   - The setup script installs 3b1b's manim automatically

### Performance Notes:
- First run after setup may be slower (model downloads)
- Video generation typically takes 1-2 minutes
- RAG search is very fast after initial setup

## Example Topics
The system works well with mathematical concepts like:
- "derivatives and their geometric interpretation"
- "the Fourier transform explained visually"
- "linear algebra: eigenvectors and eigenvalues"
- "probability distributions and the central limit theorem"
- "complex numbers and Euler's formula"

## Technical Details

### RAG System:
- Uses sentence-transformers for semantic embeddings
- ChromaDB for vector storage and similarity search
- Retrieves top-k relevant examples for each query
- Processes 3b1b's actual video code and manim library

### 3Blue1Brown Integration:
- Uses Grant's original manim library (not ManimCommunity)
- Follows authentic 3b1b coding patterns and syntax
- Incorporates real examples from his educational videos
- Maintains his mathematical visualization style

### Video Pipeline:
1. RAG retrieval of relevant 3b1b examples
2. LLM generation of script and manim code
3. TTS conversion of script to audio
4. Manim rendering of mathematical animations
5. Final MP4 output with synchronized content

## Support
For issues or questions, check the generated log files in the output directory or review the console output during generation.
