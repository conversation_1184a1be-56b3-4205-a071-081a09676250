"""
True RAG-Enhanced LLM for Audio-First 3Blue1Brown Manim Code Generation

This module implements a comprehensive True RAG system with:
1. Multi-dimensional context analysis
2. Multi-source knowledge integration
3. Intelligent knowledge synthesis
4. Context-aware retrieval
5. Adaptive learning capabilities
"""

import os
import json
import google.generativeai as genai
from pathlib import Path
from dotenv import load_dotenv
from typing import Optional, Dict, Tuple, List
import re
import numpy as np
from dataclasses import dataclass
from datetime import datetime

try:
    from app.true_rag_system import TrueRAGSystem
except ImportError:
    print("True RAG system not available. Will be created.")
    TrueRAGSystem = None

try:
    from app.synchronized_tts import SynchronizedTTS
except ImportError:
    print("Synchronized TTS not available.")
    SynchronizedTTS = None

load_dotenv()

# API key management with automatic fallback
primary_key = os.getenv("GOOGLE_API_KEY2")
backup_key = os.getenv("GOOGLE_API_KEY")
current_api_key = primary_key

# Function to switch API keys when quota/rate limits are hit
def switch_api_key():
    """Switch between primary and backup API keys"""
    global current_api_key, primary_key, backup_key, model
    
    if current_api_key == primary_key and backup_key:
        print("🔄 Switching to backup API key due to rate/quota limits")
        current_api_key = backup_key
    elif current_api_key == backup_key and primary_key:
        print("🔄 Switching to primary API key due to rate/quota limits")
        current_api_key = primary_key
    else:
        print("⚠️ No alternative API key available")
        return False
    
    # Reconfigure with new key
    genai.configure(api_key=current_api_key)
    model = genai.GenerativeModel("gemini-2.5-flash")
    print(f"✅ API key switched successfully")
    return True

# Initial configuration
genai.configure(api_key=current_api_key)
model = genai.GenerativeModel("gemini-2.5-flash")

@dataclass
class ContextAnalysis:
    """Comprehensive context analysis results"""
    mathematical_concepts: List[Dict]
    temporal_context: Dict
    educational_context: Dict
    technical_context: Dict
    complexity_score: float
    primary_focus: str
    constraint_hierarchy: List[str]
    optimization_targets: List[str]
    risk_factors: List[str]

@dataclass
class KnowledgeSource:
    """Knowledge source configuration"""
    name: str
    type: str  # 'vector_db', 'structured_db', 'pattern_db', 'solution_db'
    content_description: str
    search_method: str
    context_fields: List[str]
    weight: float = 1.0

@dataclass
class RetrievedKnowledge:
    """Retrieved knowledge from multiple sources"""
    manim_examples: List[Dict]
    mathematical_concepts: List[Dict]
    animation_patterns: List[Dict]
    error_solutions: List[Dict]
    timing_strategies: List[Dict]
    relevance_scores: Dict[str, float]
    confidence_scores: Dict[str, float]

class TrueRAGEnhancedManimLLM:
    """
    True RAG-Enhanced Manim LLM with comprehensive context analysis
    and multi-source knowledge integration
    """
    
    def __init__(self, rag_db_path: str = "true_rag_db"):
        """Initialize the True RAG Enhanced LLM"""
        self.rag_db_path = rag_db_path
        self.rag_system = None
        self.tts = None
        
        # Initialize knowledge sources
        self.knowledge_sources = self._initialize_knowledge_sources()
        
        # Initialize context analysis components
        self.context_analyzer = ContextAnalyzer()
        self.knowledge_synthesizer = None  # Simplified for now
        self.adaptive_learner = None  # Simplified for now
        
        # Performance tracking
        self.generation_history = []
        self.success_patterns = {}
        self.failure_patterns = {}
        
        print("🧠 True RAG Enhanced Manim LLM initialized")
        
    def _initialize_knowledge_sources(self) -> Dict[str, KnowledgeSource]:
        """Initialize all knowledge sources for the True RAG system"""
        return {
            'manim_codebase': KnowledgeSource(
                name='manim_codebase',
                type='vector_db',
                content_description='3b1b_examples_with_metadata',
                search_method='semantic_similarity',
                context_fields=['concept', 'complexity', 'timing', 'objects_used'],
                weight=1.0
            ),
            'mathematical_knowledge': KnowledgeSource(
                name='mathematical_knowledge',
                type='structured_db',
                content_description='concept_definitions_relationships_visualizations',
                search_method='graph_traversal',
                context_fields=['prerequisite', 'difficulty', 'visual_patterns'],
                weight=0.8
            ),
            'animation_patterns': KnowledgeSource(
                name='animation_patterns',
                type='pattern_db',
                content_description='timing_strategies_visual_flows_transitions',
                search_method='pattern_matching',
                context_fields=['duration', 'complexity', 'sync_requirements'],
                weight=0.9
            ),
            'error_knowledge': KnowledgeSource(
                name='error_knowledge',
                type='solution_db',
                content_description='common_errors_solutions_prevention',
                search_method='symptom_matching',
                context_fields=['error_type', 'context', 'solution_pattern'],
                weight=0.7
            ),
            'timing_knowledge': KnowledgeSource(
                name='timing_knowledge',
                type='pattern_db',
                content_description='audio_video_synchronization_patterns',
                search_method='temporal_matching',
                context_fields=['duration', 'pacing', 'sync_points'],
                weight=0.8
            )
        }

    def generate_audio_first_video(self, prompt: str) -> Dict:
        """
        Generate audio-first video using True RAG system

        Args:
            prompt: User's topic/request

        Returns:
            Dict containing all generated content and metadata
        """
        print("🧠 Starting True RAG Audio-First Generation")
        print("=" * 60)

        try:
            # Step 1: Generate natural educational script
            print("📝 Step 1: Generating context-aware educational script...")
            script = self.generate_natural_script(prompt)

            # Step 2: Create audio at natural speaking pace
            print("🎵 Step 2: Creating audio with optimal pacing...")
            audio_result = self.create_natural_audio(script)

            # Step 3: Perform comprehensive context analysis
            print("🧠 Step 3: Performing multi-dimensional context analysis...")
            context = self.context_analyzer.analyze_context(prompt, script, audio_result.get('timing', {}))

            # Step 4: Retrieve and synthesize knowledge
            print("📚 Step 4: Retrieving and synthesizing multi-source knowledge...")
            knowledge = self.retrieve_and_synthesize_knowledge(context)

            # Step 5: Generate synchronized manim code
            print("🎬 Step 5: Generating context-aware synchronized code...")
            code = self.generate_synchronized_manim_code(prompt, script, audio_result['timing'], context, knowledge)

            # Step 6: Learn from results
            print("🎯 Step 6: Learning from generation results...")
            self.adaptive_learner.learn_from_result(context, {'script': script, 'code': code}, True)

            return {
                'script': script,
                'audio_file': audio_result['audio_file'],
                'audio_timing': audio_result['timing'],
                'manim_code': code,
                'context_analysis': context,
                'knowledge_synthesis': knowledge,
                'success': True
            }

        except Exception as e:
            print(f"❌ True RAG generation failed: {e}")
            return {'success': False, 'error': str(e)}

    def generate_natural_script(self, prompt: str) -> str:
        """Generate natural educational script with context awareness"""

        # Simple script generation for now
        return f"""
Let's explore {prompt} together.

This is a fascinating topic in mathematics that connects to many areas of our daily lives.

To understand this concept, we'll start with the basic intuition, then build up to the more formal mathematical description.

Think of it this way: mathematics is all about patterns and relationships, and {prompt} is a perfect example of how these patterns emerge naturally.

We'll visualize this step by step, making the abstract concrete and the complex simple.

By the end, you'll have a clear understanding of not just what {prompt} is, but why it matters and how it connects to the bigger picture of mathematics.

Let's dive in and see what makes this concept so beautiful and powerful.
"""

    def create_natural_audio(self, script: str) -> Dict:
        """Create natural-paced audio with timing analysis"""

        # Simple timing estimation for now
        words = len(script.split())
        estimated_duration = words / 150 * 60  # Assume 150 words per minute

        return {
            'audio_file': None,
            'timing': {
                'total_duration': estimated_duration,
                'segments': [
                    {'start': 0, 'end': estimated_duration/3, 'text': 'Introduction'},
                    {'start': estimated_duration/3, 'end': 2*estimated_duration/3, 'text': 'Main content'},
                    {'start': 2*estimated_duration/3, 'end': estimated_duration, 'text': 'Conclusion'}
                ]
            }
        }

    def retrieve_and_synthesize_knowledge(self, context: ContextAnalysis) -> Dict:
        """Retrieve knowledge from multiple sources and synthesize intelligently"""

        # Simple knowledge synthesis for now
        return {
            'primary_guidance': 'Use clear mathematical visualizations',
            'supporting_examples': ['Basic function graphs', 'Simple animations'],
            'constraint_warnings': ['Avoid complex LaTeX', 'Keep timing synchronized'],
            'optimization_suggestions': ['Use built-in manim objects', 'Maintain visual clarity'],
            'knowledge_gaps': [],
            'integration_strategy': 'Progressive complexity building'
        }

    def generate_synchronized_manim_code(self, prompt: str, script: str, audio_timing: Dict,
                                       context: ContextAnalysis, knowledge: Dict) -> str:
        """Generate manim code synchronized with script using LLM + RAG examples"""

        print("🎬 Generating LLM-driven script-synchronized manim code...")
        print(f"📝 Script preview: {script[:100]}...")

        # Get relevant examples from RAG system
        relevant_examples = self._get_relevant_rag_examples(prompt, script, context)

        # Generate code using LLM with RAG guidance
        return self._generate_llm_synchronized_code(prompt, script, audio_timing, context, relevant_examples)

    def _analyze_script_content(self, script: str) -> Dict:
        """Analyze script content for synchronization cues"""

        sentences = script.split('.')

        analysis = {
            'sentences': [s.strip() for s in sentences if s.strip()],
            'key_phrases': [],
            'mathematical_terms': [],
            'action_cues': [],
            'timing_cues': []
        }

        # Extract key phrases and mathematical terms
        math_terms = ['derivative', 'tangent', 'slope', 'function', 'vector', 'addition', 'subtraction',
                     'area', 'integral', 'curve', 'theorem', 'proof', 'equation', 'graph']

        action_cues = ['let\'s', 'now', 'first', 'next', 'then', 'finally', 'see', 'look', 'show', 'visualize']

        script_lower = script.lower()

        for term in math_terms:
            if term in script_lower:
                analysis['mathematical_terms'].append(term)

        for cue in action_cues:
            if cue in script_lower:
                analysis['action_cues'].append(cue)

        return analysis

    def _get_relevant_rag_examples(self, prompt: str, script: str, context: ContextAnalysis) -> str:
        """Get relevant examples from RAG database to guide LLM generation"""

        # Use the existing RAG retrieval system
        query = f"{prompt} {script[:200]}"  # Combine prompt and script for better matching

        try:
            # Get relevant documents from RAG
            relevant_docs = self.rag_retriever.get_relevant_documents(query)

            # Extract code examples and patterns
            examples = []
            for doc in relevant_docs[:3]:  # Top 3 most relevant
                content = doc.page_content
                if 'class' in content and 'Scene' in content:
                    examples.append(content)

            return "\n\n".join(examples) if examples else ""

        except Exception as e:
            print(f"⚠️ RAG retrieval failed: {e}")
            return ""

    def _generate_llm_synchronized_code(self, prompt: str, script: str, audio_timing: Dict,
                                      context: ContextAnalysis, rag_examples: str) -> str:
        """Generate synchronized manim code using LLM with RAG guidance"""

        # Prepare script timing information
        sentences = [s.strip() for s in script.split('.') if s.strip()]
        total_duration = audio_timing.get('total_duration', 60)
        sentence_duration = total_duration / len(sentences) if sentences else 3

        # Create comprehensive prompt for LLM
        llm_prompt = f"""
Generate a 3Blue1Brown manim animation that is perfectly synchronized with this educational script.

TOPIC: {prompt}

SCRIPT TO ANIMATE:
{script}

TIMING REQUIREMENTS:
- Total duration: {total_duration:.1f} seconds
- Number of sentences: {len(sentences)}
- Time per sentence: {sentence_duration:.1f} seconds

SYNCHRONIZATION REQUIREMENTS:
1. Create animations that match EXACTLY what is being said in each sentence
2. Use self.wait({sentence_duration:.1f}) after each sentence's animation
3. Include comments showing which sentence each animation represents
4. Make sure visual elements appear when they are mentioned in the script
5. Use appropriate mathematical visualizations for the content

SCRIPT SENTENCES:
{chr(10).join(f"{i+1}. {sentence}" for i, sentence in enumerate(sentences))}

EXAMPLE PATTERNS (use as reference, don't copy exactly):
{rag_examples[:1000] if rag_examples else "No specific examples available"}

REQUIREMENTS:
- Use 3Blue1Brown manim syntax (manimlib)
- Class name must be "TrueRAGScene"
- Include proper imports: from manimlib import *
- Synchronize animations with script sentences
- Use appropriate colors and mathematical objects
- Include timing comments for each sentence
- Generate ONLY the Python code, no explanations

Generate the complete manim code:
"""

        try:
            # Generate code using LLM
            response = self.model.generate_content(llm_prompt)
            generated_code = response.text.strip()

            # Clean up the response to extract just the code
            if '```python' in generated_code:
                generated_code = generated_code.split('```python')[1].split('```')[0]
            elif '```' in generated_code:
                generated_code = generated_code.split('```')[1].split('```')[0]

            # Ensure proper imports and class structure
            if 'from manimlib import *' not in generated_code:
                generated_code = 'from manimlib import *\n\n' + generated_code

            if 'class TrueRAGScene' not in generated_code:
                # Wrap in proper class if needed
                generated_code = f"""from manimlib import *

class TrueRAGScene(Scene):
    def construct(self):
{chr(10).join('        ' + line for line in generated_code.split(chr(10)) if line.strip())}
"""

            print("✅ LLM-generated synchronized code created")
            return generated_code

        except Exception as e:
            print(f"❌ LLM code generation failed: {e}")
            # Fallback to a simple synchronized template
            return self._generate_fallback_synchronized_code(prompt, script, sentences, sentence_duration)

    def _generate_fallback_synchronized_code(self, prompt: str, script: str, sentences: list, sentence_duration: float) -> str:
        """Generate fallback synchronized code when LLM fails"""

        title = prompt.title()

        return f'''from manimlib import *

class TrueRAGScene(Scene):
    def construct(self):
        # Sentence 1: "{sentences[0] if sentences else 'Introduction'}"
        title = Text("{title}", font_size=72)
        self.play(Write(title))
        self.wait({sentence_duration:.1f})
        self.play(FadeOut(title))

        # Sentence 2: "{sentences[1] if len(sentences) > 1 else 'Mathematical content'}"
        axes = Axes(x_range=(-3, 3, 1), y_range=(-2, 8, 1))
        self.play(ShowCreation(axes))
        self.wait({sentence_duration:.1f})

        # Sentence 3: "{sentences[2] if len(sentences) > 2 else 'Visualization'}"
        func = axes.get_graph(lambda x: x**2, color=BLUE)
        func_label = Text("Mathematical Function", font_size=48, color=BLUE)
        func_label.next_to(func, UP)

        self.play(ShowCreation(func))
        self.play(Write(func_label))
        self.wait({sentence_duration:.1f})

        # Remaining sentences: Show explanation
        explanation = Text("Educational Content", font_size=36)
        explanation.to_edge(DOWN)
        self.play(Write(explanation))

        # Wait for remaining sentences
        remaining_time = max((len(sentences) - 3) * sentence_duration, sentence_duration)
        self.wait(remaining_time)

        # Cleanup
        self.play(FadeOut(axes), FadeOut(func), FadeOut(func_label), FadeOut(explanation))
'''


class ContextAnalyzer:
    """Analyzes educational content context for enhanced generation"""

    def __init__(self):
        self.mathematical_domains = {
            'algebra': ['equation', 'variable', 'solve', 'polynomial', 'quadratic', 'linear'],
            'calculus': ['derivative', 'integral', 'limit', 'tangent', 'slope', 'rate'],
            'geometry': ['triangle', 'circle', 'angle', 'area', 'perimeter', 'volume'],
            'trigonometry': ['sine', 'cosine', 'tangent', 'angle', 'triangle', 'periodic'],
            'statistics': ['probability', 'mean', 'median', 'distribution', 'sample'],
            'linear_algebra': ['vector', 'matrix', 'dot product', 'cross product', 'linear'],
            'discrete': ['graph', 'tree', 'combinatorics', 'permutation', 'fibonacci']
        }

    def analyze_context(self, prompt: str, script: str = "", timing_info: dict = None) -> ContextAnalysis:
        """Analyze the context of educational content"""

        combined_text = f"{prompt} {script}".lower()

        # Determine primary mathematical focus
        domain_scores = {}
        for domain, keywords in self.mathematical_domains.items():
            score = sum(1 for keyword in keywords if keyword in combined_text)
            if score > 0:
                domain_scores[domain] = score

        primary_focus = max(domain_scores, key=domain_scores.get) if domain_scores else 'general_math'

        # Calculate complexity score
        complexity_indicators = ['advanced', 'complex', 'difficult', 'theorem', 'proof', 'analysis']
        complexity_score = sum(1 for indicator in complexity_indicators if indicator in combined_text) / 10
        complexity_score = min(max(complexity_score, 0.2), 1.0)  # Clamp between 0.2 and 1.0

        # Extract mathematical concepts
        mathematical_concepts = []
        for domain, keywords in self.mathematical_domains.items():
            for keyword in keywords:
                if keyword in combined_text:
                    mathematical_concepts.append({
                        'concept': keyword,
                        'category': domain,
                        'complexity': 'basic' if complexity_score < 0.5 else 'advanced'
                    })

        return ContextAnalysis(
            primary_focus=f"{primary_focus}_focused",
            complexity_score=complexity_score,
            mathematical_concepts=mathematical_concepts[:10],  # Top 10
            temporal_context={'duration': 60, 'pacing': 'moderate'},
            educational_context={'level': 'undergraduate' if complexity_score > 0.6 else 'high_school'},
            technical_context={'visualization_needs': ['graphs', 'equations', 'animations']},
            constraint_hierarchy=['accuracy', 'clarity', 'engagement'],
            optimization_targets=['understanding', 'retention'],
            risk_factors=['complexity_overload', 'pacing_issues']
        )
