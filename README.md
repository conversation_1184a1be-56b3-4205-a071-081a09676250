# 3Blue1Brown Video Generator

A professional AI-powered system that generates educational mathematics videos in the style of 3Blue1Brown using RAG (Retrieval-Augmented Generation) and manim animations.

## 🚀 Features

- **🎬 AI-Generated Educational Videos** - Create 3Blue1Brown-style mathematical animations
- **🗣️ Natural Language Input** - Simply describe the mathematical concept you want to visualize
- **💻 Professional Web Interface** - Modern React frontend with video playback
- **📊 Real-time Progress Tracking** - Monitor video generation with live updates
- **🎥 Multiple Output Formats** - MP4 videos with synchronized audio
- **🔊 Text-to-Speech Narration** - AI-generated educational narration
- **📝 Source Code Generation** - Get the manim Python code for each animation
- **📱 Responsive Design** - Works on desktop, tablet, and mobile devices
- **🧠 Enhanced Error Handling** - Advanced RAG-based error correction with 10 compilation attempts
- **🔍 Context-Aware RAG** - Retrieves relevant 3Blue1Brown examples for error fixing

## 📋 Prerequisites

- **Python 3.11+** - Core backend system
- **Node.js 16+** - React frontend
- **FFmpeg** - Video/audio processing (must be in PATH)
- **Google Gemini API Key** - AI language model access

## 🛠️ Installation

### 1. <PERSON><PERSON> the Repository

```bash
git clone https://github.com/Talal1904/tutorial-video-generator-using-manim.git
cd tutorial-video-generator-using-manim
```

### 2. Set Up Python Environment

```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
# Windows:
.venv\Scripts\activate
# Linux/Mac:
source .venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt
```

### 3. Configure Environment Variables

Create a `.env` file in the root directory:

```env
GOOGLE_API_KEY2=your_google_gemini_api_key_here
```

### 4. Initialize the RAG System

```bash
# Download 3Blue1Brown repositories and build RAG database
python setup_3b1b_rag.py
```

*Note: Initial setup takes 15-20 minutes and requires ~2GB disk space. This downloads 3Blue1Brown's manim and videos repositories and builds the RAG database with 46,339+ code examples.*

### 5. Install Frontend Dependencies

```bash
cd frontend_react
npm install
cd ..
```

## 🚀 Quick Start

### Option 1: Web Interface (Recommended)

1. **Start the API Server:**
```bash
uvicorn api_server:app --host 0.0.0.0 --port 8000
```

2. **Start the React Frontend:**
```bash
cd frontend_react
npm start
```

3. **Access the Application:**
   - **Frontend**: http://localhost:3000
   - **API**: http://localhost:8000
   - **API Docs**: http://localhost:8000/docs

### Option 2: Command Line Interface

**Generate MP4 video:**
```bash
python main_rag_3b1b.py "derivatives and tangent lines"
```

**Interactive mode:**
```bash
python main_rag_3b1b.py
```

## 🌐 Web Interface Features

### React Frontend
- 🎬 **Video Generation** - Enter topics and generate videos
- 📊 **Real-time Progress** - Live updates during generation
- 🎥 **Video Playback** - Watch videos directly in browser
- 🔊 **Audio Playback** - Listen to narration
- 📝 **Script Reading** - View educational content
- 📁 **File Downloads** - Download all generated files
- 📚 **Job History** - Track all previous generations
- 📱 **Responsive Design** - Works on all devices

## 🔧 API Server

**FastAPI REST server for programmatic access:**

```bash
# Start the API server
uvicorn api_server:app --host 0.0.0.0 --port 8000

# Server runs on http://localhost:8000
# Interactive API docs: http://localhost:8000/docs
```

**API Usage Example:**
```python
import requests

# Generate video via API
response = requests.post('http://localhost:8000/generate-audio-first', json={
    'topic': 'derivatives and tangent lines'
})

job = response.json()
job_id = job['job_id']

# Check status
status = requests.get(f'http://localhost:8000/status/{job_id}')
print(status.json())

# Download files when complete
if status.json()['status'] == 'completed':
    video_url = f"http://localhost:8000/download/{job_id}/video.mp4"
    audio_url = f"http://localhost:8000/download/{job_id}/voiceover.mp3"
```

## 📁 Output Structure

```
generated/
└── [job_id]/
    ├── script.txt              # Educational script
    ├── manim_code.py           # 3Blue1Brown manim code
    ├── voiceover.mp3           # TTS narration
    ├── rag_examples.txt        # Retrieved 3b1b examples
    └── [SceneName].mp4         # Final video with synchronized audio
```

## 🛠️ How It Works

### Audio-First Generation Pipeline
1. **Script Generation**: Creates educational content using AI
2. **Audio Synthesis**: Generates natural TTS narration with timing
3. **RAG Retrieval**: Searches 46,339+ code examples from 3Blue1Brown's repositories
4. **Context-Aware Code Generation**: Creates manim code with audio timing context
5. **Enhanced Error Handling**: 10 compilation attempts with specific error fixes
6. **Video Production**: Renders animations using Grant's original manim library
7. **Audio-Video Synchronization**: Combines video with pre-generated audio

## 📚 Example Topics

- "derivatives and their geometric interpretation"
- "the Fourier transform explained visually"
- "linear algebra: eigenvectors and eigenvalues"
- "complex numbers and Euler's formula"
- "probability distributions and the central limit theorem"
- "calculus: limits and continuity"
- "neural networks and backpropagation"

## 🔧 System Architecture

### Core Components
- **`api_server.py`** - FastAPI REST server with job management
- **`app/llm_rag_audio_first.py`** - Enhanced LLM generator with error handling
- **`frontend_react/`** - React web interface with video playback
- **`app/rag_system.py`** - RAG retrieval system for 3Blue1Brown examples
- **`app/synchronized_tts.py`** - Audio-first TTS with timing

### Key Features
- **Enhanced Error Handling**: 10 compilation attempts with context-aware RAG
- **Audio-First Pipeline**: Generate audio first, then sync video
- **Real-time Progress**: WebSocket updates for frontend
- **Professional UI**: Modern React interface with video playback

## 🚀 Recent Improvements

### Enhanced Error Handling (v2.0)
- **Real Compilation Testing**: Tests actual `construct()` method execution
- **Context-Aware RAG**: Retrieves specific examples for error types (axes, area, riemann, etc.)
- **Specific Error Fixes**: Targeted guidance for common manim errors
- **10 Compilation Attempts**: More chances to fix errors with RAG examples
- **SystemExit Handling**: Proper manimlib compatibility

### Audio-First Pipeline
- **Synchronized Generation**: Audio generated first, video matches timing
- **Natural Narration**: High-quality TTS with proper pacing
- **Timing Context**: Video generation uses audio timing for better sync

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Grant Sanderson (3Blue1Brown)** for the original manim library and educational content
- **3Blue1Brown Community** for the incredible mathematical visualizations
- **Google Gemini** for the language model capabilities

## ⚠️ Disclaimer

This project uses 3Blue1Brown's code for educational purposes. All generated content should respect the original creator's work and licensing terms.