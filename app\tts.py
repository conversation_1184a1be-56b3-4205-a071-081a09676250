import pyttsx3
import re
from pathlib import Path
import os
import tempfile
from pydub import AudioSegment
# Configure ffmpeg path for pydub
ffmpeg_base = (r"C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages"
               r"\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe"
               r"\ffmpeg-7.1.1-full_build\bin")
ffmpeg_path = os.path.join(ffmpeg_base, "ffmpeg.exe")
if os.path.exists(ffmpeg_path):
    AudioSegment.converter = ffmpeg_path
    AudioSegment.ffmpeg = ffmpeg_path
    AudioSegment.ffprobe = os.path.join(ffmpeg_base, "ffprobe.exe")


def extract_sentences(text: str) -> list[str]:
    """
    Extract sentences from text that should be converted to speech.
    Filters out code blocks, comments, and other non-speech content.
    """
    # Remove code blocks (anything between ``` or ```)
    text = re.sub(r'```.*?```', '', text, flags=re.DOTALL)
    text = re.sub(r'`.*?`', '', text)

    # Remove markdown headers
    text = re.sub(r'^#+\s+', '', text, flags=re.MULTILINE)

    # Remove URLs
    url_pattern = (r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|'
                   r'[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+')
    text = re.sub(url_pattern, '', text)

    # Remove email addresses
    text = re.sub(r'\S+@\S+', '', text)

    # Remove special characters and clean up
    text = re.sub(r'[*_#\[\]()]', '', text)

    # Split into sentences using multiple delimiters
    sentences = re.split(r'[.!?]+', text)

    # Clean and filter sentences
    clean_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        # Skip empty sentences, very short ones, or ones that look like code
        code_starts = ('def ', 'class ', 'import ', 'from ')
        if (len(sentence) > 10 and
            not re.match(r'^[A-Z_]+$', sentence) and
            not re.match(r'^\d+$', sentence) and
                not sentence.startswith(code_starts)):
            clean_sentences.append(sentence)

    return clean_sentences


def text_to_speech(text: str, job_id: str) -> str:
    """
    Convert text to speech using pyttsx3 and save as MP3.
    Extracts sentences first and processes them individually.
    """
    base_path = Path(f"generated/{job_id}")
    base_path.mkdir(parents=True, exist_ok=True)

    # Extract sentences that should be converted to speech
    sentences = extract_sentences(text)

    if not sentences:
        print("No suitable sentences found for TTS conversion.")
        return ""

    print(f"Found {len(sentences)} sentences for TTS conversion:")
    for i, sentence in enumerate(sentences, 1):
        print(f"{i}. {sentence[:50]}...")

    # Initialize TTS engine
    engine = pyttsx3.init()

    # Configure TTS settings
    engine.setProperty('rate', 150)    # Speed of speech (words per minute)
    engine.setProperty('volume', 0.9)  # Volume level (0.0 to 1.0)

    # Get available voices and set a good one
    voices = engine.getProperty('voices')
    if voices:
        # Try to find a good English voice
        for voice in voices:
            if 'english' in voice.name.lower() or 'zira' in voice.name.lower():
                engine.setProperty('voice', voice.id)
                break

    # Create temporary directory for individual audio files
    temp_dir = tempfile.mkdtemp()
    audio_files = []

    try:
        # Convert each sentence to speech
        for i, sentence in enumerate(sentences):
            temp_file = os.path.join(temp_dir, f"sentence_{i:03d}.wav")

            progress = f"Converting sentence {i+1}/{len(sentences)}"
            preview = f"{sentence[:30]}..."
            print(f"{progress}: {preview}")

            # Save sentence to temporary WAV file
            engine.save_to_file(sentence, temp_file)
            engine.runAndWait()

            if os.path.exists(temp_file) and os.path.getsize(temp_file) > 0:
                audio_files.append(temp_file)

        if not audio_files:
            print("No audio files were generated.")
            return ""

        # Combine all audio files into one
        print("Combining audio files...")
        combined_audio = AudioSegment.empty()

        for audio_file in audio_files:
            try:
                audio_segment = AudioSegment.from_wav(audio_file)
                combined_audio += audio_segment
                # Add a small pause between sentences (500ms)
                combined_audio += AudioSegment.silent(duration=500)
            except Exception as e:
                print(f"Error processing {audio_file}: {e}")

        # Export as MP3 (now that ffmpeg is installed)
        output_path = base_path / "voiceover.mp3"
        combined_audio.export(str(output_path), format="mp3")

        print("✅ TTS conversion completed!")
        print(f"Audio saved to: {output_path}")
        return str(output_path)

    except Exception as e:
        print(f"Error during TTS conversion: {e}")
        return ""

    finally:
        # Clean up temporary files
        for temp_file in audio_files:
            try:
                os.remove(temp_file)
            except OSError:
                pass
        try:
            os.rmdir(temp_dir)
        except OSError:
            pass
